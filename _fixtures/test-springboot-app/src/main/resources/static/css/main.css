/* NAV Style */

/* Add a black background color to the top navigation */
.topnav {
    background-color: #333;
    overflow: hidden;
}

/* Style the links inside the navigation bar */
.topnav a {
    float: left;
    color: #f2f2f2;
    text-align: center;
    padding: 14px 16px;
    text-decoration: none;
    font-size: 17px;
}

/* Change the color of links on hover */
.topnav a:hover {
    background-color: #ddd;
    color: black;
}

/* Add a color to the active/current link */
.topnav a.active {
    background-color: #4CAF50;
    color: white;
    float: left;
}

.topnav a.right {
    float: right;
}

body {
    font-family: Arial;
    padding: 0;
    background: #f1f1f1;
    width: 80%;
    margin: auto;

}

/* Header/Blog Title */
.header {
    padding: 10px;
    font-size: 25px;
    text-align: center;
    background: white;
}

.container {
    /*width: 80%;*/
    margin: auto;
}

.posts_wrapper {
    /*width: 100%;*/
}

/* Fake image */
.img {
    float: left;
    background-color: #aaa;
    align-content: start;
    width: 20%;
    padding: 20px;
    margin: 0 20px 20px 0;

    height: 200px;
}

.post {
    background-color: white;
    padding: 20px;
    margin-top: 20px;
    text-align: center;
}

.post h2 {
    text-transform: capitalize;
}

.post p {
    text-align: justify
}

/* Clear floats after the posts */
.post:after {
    content: "";
    display: table;
    clear: both;
}

.comments h2 {
    text-align: center;
    margin: 20px auto;
}

.comment {
    position: relative;
    background-color: white;
    padding: 10px 20px;
    /*margin-top: 10px;*/
    text-align: justify;
    width: 80%;
    margin: 5px auto;
}

form {
    margin: auto;
    width: 80%;
}


/* Footer */
footer {
    padding: 20px;
    text-align: center;
    background: #ddd;
    margin-top: 20px;
}

.alert-red {
    padding: 20px;
    background-color: #f44336; /* Red */
    color: white;
    margin: 15px auto;
    width: 80%;
}
.alert-green {
    padding: 20px;
    background-color: green;
    color: white;
    margin: 15px auto;
    width: 80%;
}

a {
    color: #336655;
    text-decoration: none;
}

a:hover {
    color: #336655;
    text-decoration: underline;
}

.green-btn {
    background-color: #4CAF50; /* Green */
    border: none;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    cursor: pointer;
}

.green-btn:hover {
    background-color: #555555;
}

.red-btn {
    background-color: #f44336;
}

.blue-btn {
    background-color: #008CBA;
}

input, select {
    width: 100%;
    padding: 12px 20px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

textarea {
    width: 100%;
    height: 150px;
    padding: 12px 20px;
    box-sizing: border-box;
    border: 2px solid #ccc;
    border-radius: 4px;
    background-color: #f8f8f8;
    resize: none;
}

.float-right {
    float: right;
}

.float-left {
    float: left;
}

.align-right {
    text-align: right;
}

.align-center {
    text-align: center;
}