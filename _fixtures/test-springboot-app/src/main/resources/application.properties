### TomCat Web Server listening port
server.port = 8080

### H2 DataSource properties
spring.h2.console.enabled=true
spring.h2.console.path=/h2
spring.datasource.url=jdbc:h2:mem:blog_database;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
#spring.datasource.url=jdbc:h2:~/test
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.platform=h2
spring.datasource.schema=classpath:/sql/schema.sql
spring.datasource.data=classpath:/sql/data.sql
#spring.jpa.generate-ddl=false

spring.profiles.active=dev
#spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.hibernate.ddl-auto=update
#spring.profiles.active=prod
#spring.jpa.hibernate.ddl-auto=validate

### Thymeleaf settings
spring.thymeleaf.cache=false
# Check that the template exists before rendering it.
spring.thymeleaf.check-template=true
# Check that the templates location exists.
spring.thymeleaf.check-template-location=true
# Enable MVC Thymeleaf view resolution.
spring.thymeleaf.enabled=true
# Prefix that gets prepended to view names when building a URL.
spring.thymeleaf.prefix=classpath:/templates/
# In case i later use Web facet and place templates inside webapp/WEB-INF directory
#spring.thymeleaf.prefix=/templates/
# Suffix that gets appended to view names when building a URL.
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8

#Fixes encoding problem then loading lithuanian characters from file
spring.datasource.sql-script-encoding=UTF-8

# 7.1.5. Static Content
# By default, resources are mapped on /**, but you can tune that with the spring.mvc.static-path-pattern property. For instance, relocating all resources to /resources/** can be achieved as follows:
#spring.mvc.static-path-pattern=/**
# You can also customize the static resource locations by using the spring.resources.static-locations property (replacing the default values with a list of directory locations). The root Servlet context path, "/", is automatically added as a location as well.
#spring.resources.static-locations=


##################################################
# Log the SQL queries
logging.level.org.hibernate.SQL=DEBUG
# to log values too
logging.level.org.hibernate.type=trace
# Log the prepared statement parameters (values)
#logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
# Log more detailed info on values
logging.level.org.hibernate.type.descriptor.sql=TRACE
# Set the levels of various loggers
logging.level.org.springframework.web=info
logging.level.org.hibernate=info

# Logging levels for Web and SQL
logging.level.web=debug
logging.level.sql=debug
#logging.level.root=debug

# Set the location of a file to which to write the log
#logging.file.name=springo.log
##################################################
# Dump the queries to standard output
#spring.jpa.show-sql=true
# Pretty print the SQL queries
spring.jpa.properties.hibernate.format_sql=true


# function is not used by application so for security reasons disable it
spring.jpa.open-in-view=false