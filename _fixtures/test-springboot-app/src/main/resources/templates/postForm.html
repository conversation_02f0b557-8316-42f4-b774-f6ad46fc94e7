<!DOCTYPE html>
<html lang="en"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="/fragments/head :: head"></head>
<body>

<div th:replace="/fragments/header :: header"></div>

<div class="container">
    <form action="#"
          th:action="@{'/createNewPost'}"
          th:object="${post}"
          method="post">
        <h2>Write new blog post</h2>
        <div>
            <label>
                <div class="alert-red" th:if="${#fields.hasErrors('title')}" th:errors="*{title}">Title Error</div>
                <input type="text" th:field="*{title}" placeholder="Title"/>
            </label>
        </div>
        <div>
            <label>
                <div class="alert-red" th:if="${#fields.hasErrors('body')}" th:errors="*{body}">Body Error</div>
                <textarea th:field="*{body}" placeholder="Write something valuable"></textarea>
            </label>
        </div>
        <div class="float-left">
            <button class="green-btn" type="submit">Send</button>
        </div>
    </form>
    <div class="float-right" th:if="${post.id}">
        <a th:href="@{'/deletePost/{id}'(id=${post.id})}">
            <button class="green-btn red-btn" type="button">Delete Post</button>
        </a>
    </div>
</div>


<footer th:replace="/fragments/footer :: footer"></footer>

</body>
</html>