# Spring Boot 迁移工具配置文件示例
# 复制此文件并根据需要修改配置

migration:
  # 目标版本配置
  target-versions:
    spring-boot: "3.2.0"
    java: "21"
  
  # 迁移规则配置
  rules:
    # javax -> jakarta 包名转换
    - name: "javax-to-jakarta"
      enabled: true
      priority: 1
      parameters:
        # 排除的包（如果有特殊需求）
        exclude-packages: []
        # 是否备份原文件
        backup-original: true
    
    # Spring Security 配置升级
    - name: "spring-security-config"
      enabled: true
      priority: 2
      parameters:
        # 是否更新方法级安全配置
        update-method-security: true
        # 是否转换WebSecurityConfigurerAdapter
        convert-web-security-adapter: true
    
    # Spring Boot 配置属性迁移
    - name: "spring-boot-properties"
      enabled: true
      priority: 3
      parameters:
        # 是否移除已废弃的配置
        remove-deprecated: true
        # 是否添加新的推荐配置
        add-recommended: true
    
    # JDK 21 新特性应用（可选）
    - name: "jdk21-features"
      enabled: false  # 默认关闭，需要手动启用
      priority: 10
      parameters:
        # 是否应用模式匹配
        apply-pattern-matching: true
        # 是否应用文本块
        apply-text-blocks: true
        # 是否建议使用记录类
        suggest-records: true
  
  # AI服务配置
  ai:
    provider: "phodal"  # 支持: phodal, openai, claude
    max-retries: 3
    timeout-seconds: 30
    # API密钥（如果需要）
    # api-key: "your-api-key-here"
    
    # AI修复配置
    auto-fix:
      enabled: true
      max-attempts: 5
      # 需要AI修复的错误类型
      error-types:
        - "compilation-error"
        - "deprecated-api"
        - "import-error"
  
  # 构建配置
  build:
    # 构建命令（根据项目类型自动检测）
    # command: "mvn clean compile"  # Maven项目
    # command: "./gradlew build"    # Gradle项目
    
    max-attempts: 5
    timeout-minutes: 10
    
    # 跳过的测试类型
    skip-tests:
      - "integration-tests"
      # - "unit-tests"  # 如果需要跳过单元测试
  
  # 报告配置
  report:
    formats: ["markdown", "json"]  # 支持: markdown, json, html
    output-dir: "migration-reports"
    include-details: true
    
    # 报告内容配置
    sections:
      - "summary"
      - "changes"
      - "warnings"
      - "recommendations"
  
  # 备份配置
  backup:
    enabled: true
    # 备份目录（相对于项目根目录）
    directory: "../backups"
    # 备份文件名格式
    name-pattern: "{project-name}_backup_{timestamp}"
    # 是否压缩备份
    compress: true
  
  # 验证配置
  validation:
    # 迁移前验证
    pre-migration:
      - "project-structure"
      - "dependencies"
      - "java-version"
    
    # 迁移后验证
    post-migration:
      - "compilation"
      - "tests"
      - "startup"
  
  # 日志配置
  logging:
    level: "INFO"  # DEBUG, INFO, WARN, ERROR
    file: "migration.log"
    max-size: "10MB"
    max-files: 5
