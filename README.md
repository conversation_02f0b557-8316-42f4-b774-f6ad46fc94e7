# Spring Boot 2.x → 3.x + JDK 8 → 21 迁移工具架构设计

## 📋 概述

基于 Vue 2 → Vue 3 迁移工具的成功架构，设计一个针对 Spring Boot 2.x 升级到 3.x 以及 JDK 8 升级到 JDK 21 的自动化迁移工具。

## 🏗️ 核心架构

### 主要组件

```
JavaAutoMigrator (主编排器)
├── ConfigMigrator (配置文件迁移)
├── DependencyMigrator (依赖升级)
├── CodeMigrator (代码转换)
├── BuildFixer (构建修复)
├── TestRunner (测试执行)
├── RuntimeValidator (运行时验证)
└── ReportGenerator (报告生成)
```

## 🔄 迁移流程 (8个步骤)

### 1. 项目分析与备份
- **目标**: 分析项目结构，创建备份
- **工具**: JavaParser + ASM
- **输出**: 项目分析报告，备份文件

### 2. 配置文件迁移
- **目标**: 升级 application.yml/properties, pom.xml/build.gradle
- **工具**: XML/YAML 解析器 + AI 智能合并
- **关键变更**:
  - Spring Boot 3.x 配置属性变更
  - JDK 21 兼容性配置
  - 移除过时配置项

### 3. 依赖升级
- **目标**: 升级 Maven/Gradle 依赖
- **工具**: Maven/Gradle API + 依赖映射表
- **关键变更**:
  - Spring Boot 2.x → 3.x
  - Java EE → Jakarta EE
  - 第三方库兼容性升级

### 4. 代码转换
- **目标**: 自动转换不兼容的代码
- **工具**: JavaParser + OpenRewrite + 自定义规则
- **关键变更**:
  - javax.* → jakarta.*
  - 过时 API 替换
  - 新语法特性应用

### 5. 构建修复
- **目标**: 解决编译错误
- **工具**: Maven/Gradle + AI 错误分析
- **策略**: 迭代式修复，AI 辅助分析

### 6. 测试执行
- **目标**: 运行单元测试和集成测试
- **工具**: JUnit 5 + TestContainers
- **验证**: 功能回归测试

### 7. 运行时验证
- **目标**: 启动应用并验证核心功能
- **工具**: Spring Boot Actuator + 健康检查
- **验证**: API 端点、数据库连接、缓存等

### 8. 报告生成
- **目标**: 生成详细的迁移报告
- **内容**: 变更摘要、风险点、手动处理项

## 🛠️ 技术栈选择

### CLI 框架
```java
@Command(name = "java-migrator", description = "Spring Boot 2.x → 3.x 迁移工具")
public class JavaMigratorCLI implements Callable<Integer> {
    @Parameters(index = "0", description = "项目路径")
    private String projectPath;
    
    @Option(names = "--target-path", description = "目标路径 (可选，默认就地迁移)")
    private String targetPath;
    
    @Option(names = "--dry-run", description = "预览模式")
    private boolean dryRun = false;
}
```

### 代码生成
```java
// 使用 JavaPoet 生成新代码
TypeSpec migratedClass = TypeSpec.classBuilder("MigratedController")
    .addModifiers(Modifier.PUBLIC)
    .addAnnotation(ClassName.get("org.springframework.web.bind.annotation", "RestController"))
    .addMethod(MethodSpec.methodBuilder("migrate")
        .addModifiers(Modifier.PUBLIC)
        .returns(String.class)
        .addStatement("return $S", "Migrated successfully")
        .build())
    .build();
```

### 源码解析
```java
// JavaParser 解析 Java 代码
CompilationUnit cu = StaticJavaParser.parse(sourceFile);
cu.accept(new VoidVisitorAdapter<Void>() {
    @Override
    public void visit(ImportDeclaration n, Void arg) {
        if (n.getNameAsString().startsWith("javax.")) {
            // 转换为 jakarta.*
            String newImport = n.getNameAsString().replace("javax.", "jakarta.");
            n.setName(newImport);
        }
        super.visit(n, arg);
    }
}, null);
```

## 📊 架构图

```mermaid
graph TB
    A[JavaAutoMigrator] --> B[ConfigMigrator]
    A --> C[DependencyMigrator]
    A --> D[CodeMigrator]
    A --> E[BuildFixer]
    A --> F[TestRunner]
    A --> G[RuntimeValidator]
    A --> H[ReportGenerator]
    
    B --> B1[ApplicationConfigMigrator]
    B --> B2[WebConfigMigrator]
    B --> B3[SecurityConfigMigrator]
    
    C --> C1[MavenDependencyUpgrader]
    C --> C2[GradleDependencyUpgrader]
    C --> C3[ThirdPartyMapper]
    
    D --> D1[JavaxToJakartaMigrator]
    D --> D2[APIDeprecationMigrator]
    D --> D3[JDK21FeatureMigrator]
    
    E --> E1[CompileErrorAnalyzer]
    E --> E2[AIFixAgent]
    E --> E3[BuildExecutor]
    
    F --> F1[UnitTestRunner]
    F --> F2[IntegrationTestRunner]
    F --> F3[TestResultAnalyzer]
    
    G --> G1[ApplicationStarter]
    G --> G2[HealthChecker]
    G --> G3[APIValidator]
```

## 🔧 核心组件设计

### 1. JavaAutoMigrator (主编排器)
```java
public class JavaAutoMigrator {
    private final String projectPath;
    private final String targetPath;
    private final MigrationOptions options;
    private final List<MigrationStep> steps;
    
    public MigrationResult migrate() {
        // 8步迁移流程
        for (MigrationStep step : steps) {
            StepResult result = step.execute();
            if (!result.isSuccess() && step.isRequired()) {
                return MigrationResult.failure(result.getError());
            }
        }
        return MigrationResult.success();
    }
}
```

### 2. ConfigMigrator (配置迁移器)
```java
public class ConfigMigrator implements MigrationStep {
    private final ApplicationConfigMigrator appConfigMigrator;
    private final WebConfigMigrator webConfigMigrator;
    private final SecurityConfigMigrator securityConfigMigrator;
    
    @Override
    public StepResult execute() {
        // 使用 AI 智能合并配置文件
        return aiService.mergeConfigurations(oldConfig, newTemplate);
    }
}
```

### 3. DependencyMigrator (依赖迁移器)
```java
public class DependencyMigrator implements MigrationStep {
    private final DependencyMapper dependencyMapper;
    private final VersionResolver versionResolver;
    
    @Override
    public StepResult execute() {
        // 读取 pom.xml 或 build.gradle
        // 应用依赖映射规则
        // 解决版本冲突
        return updateDependencies();
    }
}
```

### 4. CodeMigrator (代码迁移器)
```java
public class CodeMigrator implements MigrationStep {
    private final JavaParser javaParser;
    private final OpenRewriteEngine rewriteEngine;
    
    @Override
    public StepResult execute() {
        // 使用 OpenRewrite 规则进行代码转换
        List<Recipe> recipes = Arrays.asList(
            new JavaxToJakartaRecipe(),
            new SpringBoot3Recipe(),
            new JDK21Recipe()
        );
        return rewriteEngine.run(recipes);
    }
}
```

## 📋 迁移规则示例

### 依赖映射表
```yaml
dependencies:
  spring-boot:
    from: "2.7.x"
    to: "3.2.x"
    breaking_changes:
      - "配置属性重命名"
      - "自动配置类变更"
  
  javax-to-jakarta:
    mappings:
      "javax.servlet": "jakarta.servlet"
      "javax.persistence": "jakarta.persistence"
      "javax.validation": "jakarta.validation"
```

### OpenRewrite 规则
```java
@Recipe(
    displayName = "Migrate javax to jakarta",
    description = "Replace javax.* imports with jakarta.*"
)
public class JavaxToJakartaRecipe extends Recipe {
    @Override
    public TreeVisitor<?, ExecutionContext> getVisitor() {
        return new JavaIsoVisitor<ExecutionContext>() {
            @Override
            public J.Import visitImport(J.Import import_, ExecutionContext ctx) {
                if (import_.getQualid().printTrimmed().startsWith("javax.")) {
                    return import_.withQualid(
                        import_.getQualid().withName(
                            import_.getQualid().getName().replace("javax.", "jakarta.")
                        )
                    );
                }
                return import_;
            }
        };
    }
}
```

## 🚀 CLI 使用示例

```bash
# 完整迁移
java-migrator migrate /path/to/spring-boot-project

# 就地迁移
java-migrator migrate /path/to/project --in-place

# 预览模式
java-migrator migrate /path/to/project --dry-run

# 跳过特定步骤
java-migrator migrate /path/to/project --skip-steps config,test

# 自定义构建命令
java-migrator migrate /path/to/project --build-command "mvn clean compile"

# 详细输出
java-migrator migrate /path/to/project --verbose
```

## 📈 AI 集成策略

### 错误分析与修复
```java
public class JavaBuildFixAgent extends AIService {
    public FixResult analyzeCompileError(String errorOutput) {
        String prompt = buildPrompt(errorOutput, projectContext);
        AIResponse response = callAI(prompt);
        return parseFixSuggestions(response);
    }
    
    public CodeFix generateCodeFix(String filePath, String errorContext) {
        // 类似 Vue 工具的 AI 修复机制
        return aiService.generateJavaCodeFix(filePath, errorContext);
    }
}
```

## 📊 统计与报告

### 迁移统计
```java
public class MigrationStats {
    private int filesAnalyzed;
    private int filesModified;
    private int dependenciesUpgraded;
    private int errorsFixed;
    private int testsRun;
    private int testsPassed;
    private Duration totalDuration;
}
```

### 报告格式
- **Markdown**: 人类可读的详细报告
- **JSON**: 机器可读的结构化数据
- **HTML**: 可视化的交互式报告

## 🔍 风险评估

### 高风险项
1. **数据库迁移**: JPA/Hibernate 版本升级
2. **安全配置**: Spring Security 6.x 变更
3. **Web 配置**: Servlet API 变更
4. **第三方集成**: 兼容性问题

### 缓解策略
1. **渐进式迁移**: 分步骤验证
2. **回滚机制**: 自动备份和恢复
3. **兼容性检查**: 预先验证依赖兼容性
4. **人工审核**: 关键变更需要确认

## 📝 下一步计划

1. **原型开发**: 实现核心 JavaAutoMigrator
2. **规则库建设**: 收集常见迁移模式
3. **AI 模型训练**: 针对 Java 迁移场景优化
4. **测试验证**: 在真实项目中验证效果
5. **社区反馈**: 收集用户使用反馈

## 🔧 详细实现方案

### 项目结构
```
java-migrator/
├── src/main/java/
│   ├── com/phodal/migrator/
│   │   ├── JavaAutoMigrator.java          # 主编排器
│   │   ├── cli/JavaMigratorCLI.java       # CLI 接口
│   │   ├── config/                        # 配置迁移
│   │   │   ├── ConfigMigrator.java
│   │   │   ├── ApplicationConfigMigrator.java
│   │   │   └── SecurityConfigMigrator.java
│   │   ├── dependency/                    # 依赖管理
│   │   │   ├── DependencyMigrator.java
│   │   │   ├── MavenDependencyUpgrader.java
│   │   │   └── GradleDependencyUpgrader.java
│   │   ├── code/                          # 代码转换
│   │   │   ├── CodeMigrator.java
│   │   │   ├── JavaxToJakartaMigrator.java
│   │   │   └── JDK21FeatureMigrator.java
│   │   ├── build/                         # 构建修复
│   │   │   ├── BuildFixer.java
│   │   │   ├── CompileErrorAnalyzer.java
│   │   │   └── JavaBuildFixAgent.java
│   │   ├── test/                          # 测试执行
│   │   │   ├── TestRunner.java
│   │   │   └── TestResultAnalyzer.java
│   │   ├── runtime/                       # 运行时验证
│   │   │   ├── RuntimeValidator.java
│   │   │   └── HealthChecker.java
│   │   ├── ai/                            # AI 服务
│   │   │   ├── JavaAIService.java
│   │   │   └── PromptBuilder.java
│   │   └── report/                        # 报告生成
│   │       ├── ReportGenerator.java
│   │       └── MigrationReport.java
├── src/main/resources/
│   ├── migration-rules/                   # 迁移规则
│   │   ├── dependency-mappings.yml
│   │   ├── config-mappings.yml
│   │   └── openrewrite-recipes.yml
│   └── templates/                         # 配置模板
│       ├── application-spring3.yml
│       └── pom-spring3-template.xml
└── pom.xml
```

### 核心依赖配置
```xml
<dependencies>
    <!-- CLI 框架 -->
    <dependency>
        <groupId>info.picocli</groupId>
        <artifactId>picocli</artifactId>
        <version>4.7.5</version>
    </dependency>

    <!-- 代码生成 -->
    <dependency>
        <groupId>com.squareup</groupId>
        <artifactId>javapoet</artifactId>
        <version>1.13.0</version>
    </dependency>

    <!-- 源码解析 -->
    <dependency>
        <groupId>com.github.javaparser</groupId>
        <artifactId>javaparser-core</artifactId>
        <version>3.25.7</version>
    </dependency>

    <!-- 代码重构 -->
    <dependency>
        <groupId>org.openrewrite</groupId>
        <artifactId>rewrite-java</artifactId>
        <version>8.21.0</version>
    </dependency>

    <!-- ASM 字节码分析 -->
    <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>9.6</version>
    </dependency>

    <!-- HTML 解析 (JSP) -->
    <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>1.17.2</version>
    </dependency>

    <!-- 模板引擎 -->
    <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>2.3.32</version>
    </dependency>
</dependencies>
```

### 关键实现示例

#### 1. 主编排器实现
```java
@Component
public class JavaAutoMigrator {
    private static final Logger logger = LoggerFactory.getLogger(JavaAutoMigrator.class);

    private final List<MigrationStep> migrationSteps;
    private final MigrationContext context;
    private final ReportGenerator reportGenerator;

    public JavaAutoMigrator(MigrationOptions options) {
        this.context = new MigrationContext(options);
        this.migrationSteps = initializeMigrationSteps();
        this.reportGenerator = new ReportGenerator(context);
    }

    public MigrationResult migrate() {
        logger.info("🚀 开始 Spring Boot 2.x → 3.x + JDK 8 → 21 迁移...");

        MigrationStats stats = new MigrationStats();
        stats.setStartTime(Instant.now());

        try {
            // 验证项目环境
            validateProject();

            // 执行8个迁移步骤
            for (int i = 0; i < migrationSteps.size(); i++) {
                MigrationStep step = migrationSteps.get(i);
                logger.info("步骤 {}/{}: {}", i + 1, migrationSteps.size(), step.getName());

                StepResult result = step.execute(context);
                stats.addStepResult(step.getName(), result);

                if (!result.isSuccess() && step.isRequired()) {
                    throw new MigrationException("关键步骤失败: " + step.getName(), result.getError());
                }

                // 更新上下文
                context.updateFromStepResult(result);
            }

            stats.setEndTime(Instant.now());
            stats.setSuccess(true);

            // 生成迁移报告
            MigrationReport report = reportGenerator.generateReport(stats);
            logger.info("✅ 迁移完成！报告已生成: {}", report.getReportPath());

            return MigrationResult.success(stats, report);

        } catch (Exception e) {
            stats.setEndTime(Instant.now());
            stats.setSuccess(false);
            stats.setError(e.getMessage());

            logger.error("❌ 迁移失败: {}", e.getMessage(), e);
            return MigrationResult.failure(e.getMessage(), stats);
        }
    }

    private List<MigrationStep> initializeMigrationSteps() {
        return Arrays.asList(
            new ProjectAnalysisStep(),      // 1. 项目分析与备份
            new ConfigMigrationStep(),      // 2. 配置文件迁移
            new DependencyMigrationStep(),  // 3. 依赖升级
            new CodeMigrationStep(),        // 4. 代码转换
            new BuildFixStep(),             // 5. 构建修复
            new TestExecutionStep(),        // 6. 测试执行
            new RuntimeValidationStep(),    // 7. 运行时验证
            new ReportGenerationStep()      // 8. 报告生成
        );
    }
}
```

#### 2. 依赖迁移器实现
```java
@Component
public class DependencyMigrator implements MigrationStep {
    private final DependencyMapper dependencyMapper;
    private final MavenDependencyUpgrader mavenUpgrader;
    private final GradleDependencyUpgrader gradleUpgrader;

    @Override
    public StepResult execute(MigrationContext context) {
        try {
            ProjectType projectType = detectProjectType(context.getProjectPath());

            switch (projectType) {
                case MAVEN:
                    return migrateMavenDependencies(context);
                case GRADLE:
                    return migrateGradleDependencies(context);
                default:
                    throw new UnsupportedOperationException("不支持的项目类型: " + projectType);
            }
        } catch (Exception e) {
            return StepResult.failure("依赖迁移失败", e);
        }
    }

    private StepResult migrateMavenDependencies(MigrationContext context) throws Exception {
        Path pomPath = context.getProjectPath().resolve("pom.xml");

        // 读取现有 pom.xml
        Document pomDoc = DocumentBuilderFactory.newInstance()
            .newDocumentBuilder()
            .parse(pomPath.toFile());

        // 应用依赖映射规则
        DependencyMappingResult mappingResult = dependencyMapper.mapDependencies(pomDoc);

        // 升级 Spring Boot 版本
        SpringBootUpgradeResult upgradeResult = mavenUpgrader.upgradeSpringBoot(pomDoc, "3.2.0");

        // 添加 Jakarta EE 依赖
        JakartaEEResult jakartaResult = mavenUpgrader.addJakartaEEDependencies(pomDoc);

        // 保存更新后的 pom.xml
        if (!context.isDryRun()) {
            savePomXml(pomDoc, pomPath);
        }

        return StepResult.success()
            .addDetail("依赖映射", mappingResult)
            .addDetail("Spring Boot 升级", upgradeResult)
            .addDetail("Jakarta EE 迁移", jakartaResult);
    }
}
```

#### 3. 代码迁移器实现
```java
@Component
public class CodeMigrator implements MigrationStep {
    private final OpenRewriteEngine rewriteEngine;
    private final JavaParser javaParser;
    private final JSPParser jspParser;

    @Override
    public StepResult execute(MigrationContext context) {
        try {
            // 收集所有需要迁移的文件
            List<Path> javaFiles = findJavaFiles(context.getProjectPath());
            List<Path> jspFiles = findJSPFiles(context.getProjectPath());

            CodeMigrationResult result = new CodeMigrationResult();

            // 应用 OpenRewrite 规则进行批量代码转换
            result.addJavaResult(migrateJavaFiles(javaFiles, context));

            // 处理 JSP 文件
            result.addJSPResult(migrateJSPFiles(jspFiles, context));

            return StepResult.success().addDetail("代码迁移结果", result);

        } catch (Exception e) {
            return StepResult.failure("代码迁移失败", e);
        }
    }

    private JavaMigrationResult migrateJavaFiles(List<Path> javaFiles, MigrationContext context) {
        // 定义 OpenRewrite 规则
        List<Recipe> recipes = Arrays.asList(
            // javax.* → jakarta.* 迁移
            new ChangePackage("javax.servlet", "jakarta.servlet", true),
            new ChangePackage("javax.persistence", "jakarta.persistence", true),
            new ChangePackage("javax.validation", "jakarta.validation", true),

            // Spring Boot 3.x 特定迁移
            new SpringBoot2To3Migration(),

            // JDK 21 特性应用
            new JDK21FeatureRecipe(),

            // 自定义业务规则
            new CustomBusinessLogicRecipe()
        );

        // 执行代码转换
        return rewriteEngine.run(recipes, javaFiles, context);
    }

    private JSPMigrationResult migrateJSPFiles(List<Path> jspFiles, MigrationContext context) {
        JSPMigrationResult result = new JSPMigrationResult();

        for (Path jspFile : jspFiles) {
            try {
                // 解析 JSP 文件
                Document jspDoc = jspParser.parse(jspFile);

                // 更新 JSP 标签库引用
                updateTagLibReferences(jspDoc);

                // 更新 EL 表达式
                updateELExpressions(jspDoc);

                // 保存更新后的文件
                if (!context.isDryRun()) {
                    saveJSPFile(jspDoc, jspFile);
                }

                result.addSuccessFile(jspFile);

            } catch (Exception e) {
                result.addFailedFile(jspFile, e.getMessage());
            }
        }

        return result;
    }
}
```

#### 4. 构建修复器实现
```java
@Component
public class JavaBuildFixer implements MigrationStep {
    private final CompileErrorAnalyzer errorAnalyzer;
    private final JavaBuildFixAgent aiFixAgent;
    private final BuildExecutor buildExecutor;

    @Override
    public StepResult execute(MigrationContext context) {
        try {
            int maxAttempts = context.getOptions().getMaxBuildAttempts();

            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                logger.info("构建尝试 {}/{}", attempt, maxAttempts);

                // 执行构建
                BuildResult buildResult = buildExecutor.build(context);

                if (buildResult.isSuccess()) {
                    logger.info("✅ 构建成功！");
                    return StepResult.success().addDetail("构建结果", buildResult);
                }

                // 分析构建错误
                logger.info("🔍 分析构建错误...");
                ErrorAnalysisResult analysisResult = errorAnalyzer.analyze(buildResult.getErrorOutput());

                if (analysisResult.getErrors().isEmpty()) {
                    logger.warn("⚠️ 无法识别构建错误，跳过修复");
                    continue;
                }

                // 使用 AI 修复错误
                logger.info("🤖 使用 AI 修复构建错误...");
                FixResult fixResult = aiFixAgent.fixErrors(analysisResult, context);

                if (fixResult.getFilesModified() > 0) {
                    logger.info("✅ AI 修复了 {} 个文件", fixResult.getFilesModified());
                } else {
                    logger.warn("⚠️ AI 未能修复任何文件");
                }
            }

            return StepResult.failure("经过 " + maxAttempts + " 次尝试仍无法修复构建错误");

        } catch (Exception e) {
            return StepResult.failure("构建修复失败", e);
        }
    }
}

@Component
public class JavaBuildFixAgent {
    private final AIService aiService;
    private final JavaParser javaParser;

    public FixResult fixErrors(ErrorAnalysisResult analysisResult, MigrationContext context) {
        FixResult result = new FixResult();

        for (CompileError error : analysisResult.getErrors()) {
            try {
                // 读取出错文件
                Path errorFile = context.getProjectPath().resolve(error.getFilePath());
                String fileContent = Files.readString(errorFile);

                // 构建 AI 提示词
                String prompt = buildFixPrompt(error, fileContent, context);

                // 调用 AI 生成修复代码
                AIResponse aiResponse = aiService.callAI(prompt);

                // 解析 AI 响应并应用修复
                CodeFix codeFix = parseCodeFix(aiResponse.getContent());

                if (codeFix.isValid()) {
                    // 应用代码修复
                    String fixedContent = applyCodeFix(fileContent, codeFix);

                    if (!context.isDryRun()) {
                        Files.writeString(errorFile, fixedContent);
                    }

                    result.addFixedFile(errorFile, codeFix.getDescription());
                }

            } catch (Exception e) {
                result.addFailedFix(error.getFilePath(), e.getMessage());
            }
        }

        return result;
    }

    private String buildFixPrompt(CompileError error, String fileContent, MigrationContext context) {
        return String.format("""
            你是一个 Spring Boot 2.x 到 3.x 迁移专家。请分析以下编译错误并提供修复方案。

            ## 项目上下文
            - 迁移类型: Spring Boot 2.x → 3.x + JDK 8 → 21
            - 文件路径: %s
            - 错误类型: %s

            ## 编译错误
            ```
            %s
            ```

            ## 当前代码
            ```java
            %s
            ```

            ## 修复要求
            1. 优先使用 Spring Boot 3.x 的新 API
            2. 确保 JDK 21 兼容性
            3. 保持代码的业务逻辑不变
            4. 提供简洁的修复方案

            请提供修复后的完整代码，格式如下：
            ```java
            // 修复后的代码
            ```

            并简要说明修复原因。
            """,
            error.getFilePath(),
            error.getErrorType(),
            error.getErrorMessage(),
            fileContent
        );
    }
}
```

#### 5. 运行时验证器实现
```java
@Component
public class RuntimeValidator implements MigrationStep {
    private final ApplicationStarter applicationStarter;
    private final HealthChecker healthChecker;
    private final APIValidator apiValidator;

    @Override
    public StepResult execute(MigrationContext context) {
        try {
            // 启动应用
            logger.info("🚀 启动迁移后的应用...");
            ApplicationInstance app = applicationStarter.start(context);

            if (!app.isRunning()) {
                return StepResult.failure("应用启动失败");
            }

            // 等待应用完全启动
            waitForApplicationReady(app, Duration.ofMinutes(2));

            RuntimeValidationResult result = new RuntimeValidationResult();

            // 健康检查
            logger.info("🔍 执行健康检查...");
            HealthCheckResult healthResult = healthChecker.check(app);
            result.setHealthCheckResult(healthResult);

            // API 验证
            logger.info("🔍 验证关键 API 端点...");
            APIValidationResult apiResult = apiValidator.validate(app, context);
            result.setApiValidationResult(apiResult);

            // 数据库连接验证
            logger.info("🔍 验证数据库连接...");
            DatabaseValidationResult dbResult = validateDatabaseConnection(app);
            result.setDatabaseValidationResult(dbResult);

            // 停止应用
            applicationStarter.stop(app);

            if (result.isAllPassed()) {
                return StepResult.success().addDetail("运行时验证", result);
            } else {
                return StepResult.warning("运行时验证发现问题").addDetail("验证结果", result);
            }

        } catch (Exception e) {
            return StepResult.failure("运行时验证失败", e);
        }
    }

    private void waitForApplicationReady(ApplicationInstance app, Duration timeout) throws InterruptedException {
        long endTime = System.currentTimeMillis() + timeout.toMillis();

        while (System.currentTimeMillis() < endTime) {
            if (app.isReady()) {
                return;
            }
            Thread.sleep(1000);
        }

        throw new RuntimeException("应用启动超时");
    }
}
```

#### 6. AI 服务集成
```java
@Service
public class JavaAIService extends AIService {
    private static final String JAVA_MIGRATION_SYSTEM_PROMPT = """
        你是一个专业的 Java 迁移专家，专门处理 Spring Boot 2.x 到 3.x 以及 JDK 8 到 21 的迁移任务。

        你的专业领域包括：
        1. Spring Boot 框架升级和配置迁移
        2. Jakarta EE 命名空间迁移 (javax.* → jakarta.*)
        3. JDK 新特性应用和兼容性处理
        4. 依赖管理和版本冲突解决
        5. 编译错误分析和代码修复
        6. 性能优化和最佳实践应用

        请始终提供准确、可执行的解决方案。
        """;

    public JavaAIService() {
        super();
        setSystemPrompt(JAVA_MIGRATION_SYSTEM_PROMPT);
    }

    public ConfigMergeResult mergeSpringBootConfig(String oldConfig, String newTemplate, MigrationContext context) {
        String prompt = buildConfigMergePrompt(oldConfig, newTemplate, context);

        try {
            AIResponse response = callAI(prompt, Map.of(
                "taskType", "config-merge",
                "configType", "spring-boot"
            ));

            return parseConfigMergeResponse(response.getContent());

        } catch (Exception e) {
            logger.error("AI 配置合并失败", e);
            return ConfigMergeResult.fallback("AI 服务不可用，使用默认合并策略");
        }
    }

    public DependencyRecommendation recommendDependencyUpgrade(String currentDependency, String targetVersion) {
        String prompt = String.format("""
            请分析以下 Maven 依赖的升级方案：

            当前依赖: %s
            目标版本: %s
            迁移场景: Spring Boot 2.x → 3.x + JDK 8 → 21

            请提供：
            1. 推荐的具体版本号
            2. 可能的兼容性问题
            3. 需要同时升级的相关依赖
            4. 配置变更建议

            以 JSON 格式返回结果。
            """, currentDependency, targetVersion);

        try {
            AIResponse response = callAI(prompt);
            return parseDependencyRecommendation(response.getContent());
        } catch (Exception e) {
            return DependencyRecommendation.defaultRecommendation(currentDependency, targetVersion);
        }
    }

    private String buildConfigMergePrompt(String oldConfig, String newTemplate, MigrationContext context) {
        return String.format("""
            请智能合并以下 Spring Boot 配置文件：

            ## 原有配置 (Spring Boot 2.x)
            ```yaml
            %s
            ```

            ## 新模板配置 (Spring Boot 3.x)
            ```yaml
            %s
            ```

            ## 合并要求
            1. 保留原有的业务配置
            2. 应用 Spring Boot 3.x 的新配置格式
            3. 移除已废弃的配置项
            4. 添加必要的新配置项
            5. 确保配置的向后兼容性

            请返回合并后的完整配置文件。
            """, oldConfig, newTemplate);
    }
}
```

## 🧪 测试策略

### 单元测试
```java
@ExtendWith(MockitoExtension.class)
class JavaAutoMigratorTest {

    @Mock
    private ConfigMigrator configMigrator;

    @Mock
    private DependencyMigrator dependencyMigrator;

    @InjectMocks
    private JavaAutoMigrator autoMigrator;

    @Test
    void shouldMigrateSuccessfully() {
        // Given
        MigrationOptions options = MigrationOptions.builder()
            .projectPath("/test/project")
            .dryRun(true)
            .build();

        when(configMigrator.execute(any())).thenReturn(StepResult.success());
        when(dependencyMigrator.execute(any())).thenReturn(StepResult.success());

        // When
        MigrationResult result = autoMigrator.migrate();

        // Then
        assertThat(result.isSuccess()).isTrue();
        verify(configMigrator).execute(any());
        verify(dependencyMigrator).execute(any());
    }
}
```

### 集成测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "migrator.ai.enabled=false",
    "migrator.dry-run=true"
})
class JavaMigratorIntegrationTest {

    @Autowired
    private JavaAutoMigrator migrator;

    @TempDir
    Path tempDir;

    @Test
    void shouldMigrateRealSpringBootProject() throws IOException {
        // 准备测试项目
        Path testProject = createTestSpringBootProject(tempDir);

        MigrationOptions options = MigrationOptions.builder()
            .projectPath(testProject.toString())
            .dryRun(true)
            .verbose(true)
            .build();

        // 执行迁移
        MigrationResult result = migrator.migrate();

        // 验证结果
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getStats().getFilesAnalyzed()).isGreaterThan(0);
    }
}
```

## 📋 配置映射规则

### dependency-mappings.yml
```yaml
# Spring Boot 2.x → 3.x 依赖映射
spring_boot_mappings:
  core:
    spring-boot-starter-parent:
      from: "2.7.x"
      to: "3.2.0"
      breaking_changes:
        - "最低 JDK 版本要求从 8 升级到 17"
        - "Spring Framework 6.x 重大变更"

    spring-boot-starter-web:
      from: "2.7.x"
      to: "3.2.0"
      additional_dependencies:
        - "spring-boot-starter-validation"  # 需要显式添加

  jakarta_ee:
    javax.servlet:
      package: "jakarta.servlet"
      artifact: "jakarta.servlet-api"
      version: "6.0.0"

    javax.persistence:
      package: "jakarta.persistence"
      artifact: "jakarta.persistence-api"
      version: "3.1.0"

    javax.validation:
      package: "jakarta.validation"
      artifact: "jakarta.validation-api"
      version: "3.0.2"

# 第三方库兼容性映射
third_party_mappings:
  database:
    mysql-connector-java:
      to: "mysql-connector-j"
      version: "8.2.0"
      reason: "MySQL 驱动重命名"

    h2:
      version: "2.2.224"
      compatibility_notes: "需要更新数据库 URL 格式"

  security:
    spring-security-oauth2:
      to: "spring-boot-starter-oauth2-authorization-server"
      version: "1.2.0"
      migration_guide: "OAuth2 配置需要重构"

# JDK 21 特性建议
jdk21_features:
  language_features:
    - name: "Record Classes"
      description: "使用 record 替代简单的数据类"
      example: "public record User(String name, String email) {}"

    - name: "Pattern Matching"
      description: "使用模式匹配简化 instanceof 检查"
      example: "if (obj instanceof String s) { return s.length(); }"

    - name: "Text Blocks"
      description: "使用文本块处理多行字符串"
      example: 'String sql = """\n    SELECT * FROM users\n    WHERE active = true\n    """;'
```

### config-mappings.yml
```yaml
# Spring Boot 配置属性映射
property_mappings:
  server:
    # 服务器配置变更
    "server.servlet.context-path":
      to: "server.servlet.context-path"
      status: "unchanged"

    "server.port":
      to: "server.port"
      status: "unchanged"

  spring:
    # 数据源配置变更
    "spring.datasource.url":
      to: "spring.datasource.url"
      notes: "H2 数据库 URL 格式可能需要更新"

    "spring.jpa.hibernate.ddl-auto":
      to: "spring.jpa.hibernate.ddl-auto"
      status: "unchanged"

    # 新增必要配置
    "spring.jpa.open-in-view":
      default_value: false
      reason: "Spring Boot 3.x 推荐显式配置"

# 移除的配置项
deprecated_properties:
  - property: "spring.jpa.hibernate.use-new-id-generator-mappings"
    reason: "在 Hibernate 6.x 中已移除"
    action: "删除此配置"

  - property: "spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation"
    reason: "不再需要此 PostgreSQL 兼容性配置"
    action: "删除此配置"

# 安全配置迁移
security_config_changes:
  web_security:
    old_pattern: "extends WebSecurityConfigurerAdapter"
    new_pattern: "使用 SecurityFilterChain Bean"
    example: |
      @Bean
      public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
          return http
              .authorizeHttpRequests(auth -> auth.anyRequest().authenticated())
              .build();
      }
```

## 🚀 使用示例

### 基本使用
```bash
# 完整迁移 (推荐)
java -jar java-migrator.jar migrate /path/to/spring-boot-project

# 预览模式 - 查看将要进行的变更
java -jar java-migrator.jar migrate /path/to/project --dry-run

# 详细输出模式
java -jar java-migrator.jar migrate /path/to/project --verbose

# 迁移到指定目录
java -jar java-migrator.jar migrate /path/to/source /path/to/target
```

### 高级选项
```bash
# 跳过特定步骤
java -jar java-migrator.jar migrate /path/to/project \
  --skip-steps config,test

# 自定义构建命令
java -jar java-migrator.jar migrate /path/to/project \
  --build-command "mvn clean compile -DskipTests"

# 设置最大构建尝试次数
java -jar java-migrator.jar migrate /path/to/project \
  --max-build-attempts 10

# 启用 AI 修复功能
java -jar java-migrator.jar migrate /path/to/project \
  --enable-ai-fix \
  --ai-provider phodal

# 生成详细报告
java -jar java-migrator.jar migrate /path/to/project \
  --report-format html,json,markdown
```

### 分步骤执行
```bash
# 1. 仅分析项目
java -jar java-migrator.jar analyze /path/to/project

# 2. 仅迁移依赖
java -jar java-migrator.jar migrate-dependencies /path/to/project

# 3. 仅转换代码
java -jar java-migrator.jar migrate-code /path/to/project

# 4. 仅修复构建
java -jar java-migrator.jar fix-build /path/to/project

# 5. 仅运行测试
java -jar java-migrator.jar run-tests /path/to/project

# 6. 仅验证运行时
java -jar java-migrator.jar validate-runtime /path/to/project
```

## 📊 迁移报告示例

### HTML 报告格式
```html
<!DOCTYPE html>
<html>
<head>
    <title>Spring Boot 迁移报告</title>
    <style>
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Spring Boot 2.7.0 → 3.2.0 迁移报告</h1>

    <h2>📊 迁移统计</h2>
    <ul>
        <li>总耗时: 5分32秒</li>
        <li>分析文件: 156个</li>
        <li>修改文件: 23个</li>
        <li>升级依赖: 15个</li>
        <li>修复错误: 8个</li>
    </ul>

    <h2>✅ 成功完成的步骤</h2>
    <ul class="success">
        <li>项目分析与备份</li>
        <li>配置文件迁移</li>
        <li>依赖升级</li>
        <li>代码转换</li>
        <li>构建修复</li>
        <li>测试执行 (98% 通过)</li>
        <li>运行时验证</li>
    </ul>

    <h2>⚠️ 需要手动处理的项目</h2>
    <ul class="warning">
        <li>自定义 OAuth2 配置需要重构 (src/main/java/config/SecurityConfig.java)</li>
        <li>数据库迁移脚本需要更新 (src/main/resources/db/migration/)</li>
    </ul>

    <h2>🔧 主要变更</h2>
    <h3>依赖升级</h3>
    <table>
        <tr><th>组件</th><th>原版本</th><th>新版本</th><th>状态</th></tr>
        <tr><td>Spring Boot</td><td>2.7.0</td><td>3.2.0</td><td class="success">✅</td></tr>
        <tr><td>Spring Security</td><td>5.7.0</td><td>6.2.0</td><td class="success">✅</td></tr>
        <tr><td>Hibernate</td><td>5.6.0</td><td>6.4.0</td><td class="success">✅</td></tr>
    </table>

    <h3>代码变更</h3>
    <ul>
        <li>javax.servlet → jakarta.servlet (12个文件)</li>
        <li>javax.persistence → jakarta.persistence (8个文件)</li>
        <li>WebSecurityConfigurerAdapter → SecurityFilterChain (3个文件)</li>
    </ul>
</body>
</html>
```

### JSON 报告格式
```json
{
  "migration_summary": {
    "project_path": "/path/to/project",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T10:05:32Z",
    "duration_seconds": 332,
    "success": true,
    "spring_boot_version": {
      "from": "2.7.0",
      "to": "3.2.0"
    },
    "jdk_version": {
      "from": "8",
      "to": "21"
    }
  },
  "statistics": {
    "files_analyzed": 156,
    "files_modified": 23,
    "dependencies_upgraded": 15,
    "errors_fixed": 8,
    "tests_run": 45,
    "tests_passed": 44,
    "tests_failed": 1
  },
  "step_results": [
    {
      "step": "project_analysis",
      "status": "success",
      "duration_seconds": 12,
      "details": {
        "project_type": "maven",
        "spring_boot_version": "2.7.0",
        "java_version": "8"
      }
    },
    {
      "step": "dependency_migration",
      "status": "success",
      "duration_seconds": 45,
      "details": {
        "upgraded_dependencies": [
          {
            "artifact": "spring-boot-starter-parent",
            "from": "2.7.0",
            "to": "3.2.0"
          }
        ]
      }
    }
  ],
  "manual_actions_required": [
    {
      "type": "security_config",
      "file": "src/main/java/config/SecurityConfig.java",
      "description": "需要重构 OAuth2 配置以适配 Spring Security 6.x",
      "priority": "high"
    }
  ]
}
```

## 🎯 总结与优势

### 核心优势

#### 1. 基于成功经验的架构设计
- **借鉴 Vue 迁移工具**：采用经过验证的8步迁移流程
- **模块化设计**：每个步骤独立可测试，支持跳过和重试
- **统一的错误处理**：一致的错误分析和修复机制

#### 2. 智能化的迁移策略
- **AI 驱动修复**：集成 phodal AI 和外部 AI 服务
- **规则引擎**：基于 OpenRewrite 的强大代码转换能力
- **智能配置合并**：AI 辅助的配置文件智能合并

#### 3. 全面的技术栈支持
- **多构建工具**：支持 Maven 和 Gradle
- **多代码类型**：Java、JSP、配置文件全覆盖
- **字节码分析**：ASM 深度分析编译后的代码

#### 4. 企业级的可靠性
- **渐进式迁移**：分步验证，降低风险
- **自动备份**：完整的回滚机制
- **详细报告**：多格式的迁移报告

### 与 Vue 迁移工具的对比

| 特性 | Vue 迁移工具 | Java 迁移工具 |
|------|-------------|---------------|
| **主编排器** | AutoMigrator.js | JavaAutoMigrator.java |
| **配置迁移** | SmartVueConfigMerge | SmartConfigMerge + AI |
| **依赖管理** | PackageJsonMigrator | DependencyMigrator (Maven/Gradle) |
| **代码转换** | VueMigrator (gogocode) | CodeMigrator (OpenRewrite) |
| **构建修复** | BuildFixer + AI | JavaBuildFixer + AI |
| **运行时验证** | PageValidator | RuntimeValidator |
| **AI 集成** | phodal AI | phodal AI + 专门的 Java 提示词 |
| **日志系统** | UnifiedLogService | 继承统一日志系统 |

### 技术创新点

#### 1. 混合式代码分析
```java
// 结合静态分析和字节码分析
public class HybridCodeAnalyzer {
    private final JavaParser staticAnalyzer;
    private final ASMAnalyzer bytecodeAnalyzer;

    public AnalysisResult analyze(Path javaFile) {
        // 静态分析获取语法结构
        CompilationUnit cu = staticAnalyzer.parse(javaFile);

        // 字节码分析获取运行时信息
        ClassNode classNode = bytecodeAnalyzer.analyze(javaFile);

        return combineResults(cu, classNode);
    }
}
```

#### 2. 智能依赖解析
```java
// AI 辅助的依赖冲突解决
public class IntelligentDependencyResolver {
    public ResolutionResult resolveDependencyConflict(
        DependencyConflict conflict,
        ProjectContext context
    ) {
        // 使用 AI 分析冲突原因和解决方案
        String prompt = buildConflictAnalysisPrompt(conflict, context);
        AIResponse response = aiService.callAI(prompt);

        return parseResolutionStrategy(response);
    }
}
```

#### 3. 渐进式验证机制
```java
// 分层验证确保迁移质量
public class ProgressiveValidator {
    public ValidationResult validate(MigrationContext context) {
        return ValidationResult.builder()
            .syntaxValidation(validateSyntax(context))      // 语法验证
            .compileValidation(validateCompilation(context)) // 编译验证
            .testValidation(validateTests(context))         // 测试验证
            .runtimeValidation(validateRuntime(context))    // 运行时验证
            .build();
    }
}
```

### 实施路线图

#### 阶段一：核心框架 (4周)
- [ ] 实现 JavaAutoMigrator 主编排器
- [ ] 完成 CLI 接口设计 (Picocli)
- [ ] 建立项目分析和备份机制
- [ ] 集成统一日志系统

#### 阶段二：基础迁移功能 (6周)
- [ ] 实现配置文件迁移器
- [ ] 完成依赖升级功能 (Maven/Gradle)
- [ ] 开发基础代码转换规则
- [ ] 建立错误分析框架

#### 阶段三：AI 增强功能 (4周)
- [ ] 集成 phodal AI 服务
- [ ] 开发智能构建修复
- [ ] 实现 AI 配置合并
- [ ] 优化提示词工程

#### 阶段四：测试与验证 (4周)
- [ ] 完善测试执行器
- [ ] 实现运行时验证
- [ ] 开发报告生成系统
- [ ] 端到端测试验证

#### 阶段五：生产就绪 (2周)
- [ ] 性能优化
- [ ] 文档完善
- [ ] 用户培训
- [ ] 生产部署

### 成功指标

#### 技术指标
- **迁移成功率**: ≥ 85% 的项目能够自动完成迁移
- **构建成功率**: ≥ 90% 的项目迁移后能够成功构建
- **测试通过率**: ≥ 95% 的原有测试在迁移后仍能通过
- **性能影响**: 迁移后应用性能下降 ≤ 5%

#### 效率指标
- **时间节省**: 相比手动迁移节省 ≥ 70% 的时间
- **人工干预**: ≤ 20% 的迁移项需要人工干预
- **错误修复**: AI 自动修复 ≥ 60% 的编译错误

#### 用户体验指标
- **易用性**: 单命令完成完整迁移
- **可观测性**: 详细的进度反馈和错误报告
- **可恢复性**: 100% 支持迁移回滚

### 风险缓解策略

#### 技术风险
1. **复杂项目兼容性**: 建立项目复杂度评估机制
2. **AI 服务稳定性**: 提供降级到规则引擎的备选方案
3. **性能问题**: 实施分批处理和内存优化

#### 业务风险
1. **数据安全**: 完整的备份和加密机制
2. **业务中断**: 支持灰度迁移和快速回滚
3. **团队接受度**: 提供详细的培训和文档

---

*本设计基于 Vue 2→3 迁移工具的成功经验，结合 Spring Boot 和 JDK 升级的特点，提供了一个完整的、可靠的、智能化的自动迁移解决方案。通过8步渐进式迁移流程、AI 驱动的智能修复、以及全面的验证机制，确保企业级 Java 应用能够安全、高效地完成技术栈升级。*
