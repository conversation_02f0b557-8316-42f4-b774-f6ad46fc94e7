# This file was generated by the Gradle 'init' task.
# https://docs.gradle.org/current/userguide/platforms.html#sub::toml-dependencies-format

[versions]
guava = "33.4.5-jre"
picocli = "4.7.5"
javaparser = "3.25.7"
openrewrite = "8.22.0"
slf4j = "2.0.7"
logback = "1.4.14"
jackson = "2.16.1"
freemarker = "2.3.32"
jsoup = "1.17.2"
asm = "9.6"
junit = "5.12.1"
mockito = "5.8.0"
assertj = "3.25.1"
openai-java = "0.18.2"
okhttp = "4.12.0"
dotenv = "3.2.0"

[libraries]
guava = { module = "com.google.guava:guava", version.ref = "guava" }

# CLI Framework
picocli = { module = "info.picocli:picocli", version.ref = "picocli" }

# Code Analysis and Transformation
javaparser-core = { module = "com.github.javaparser:javaparser-core", version.ref = "javaparser" }
openrewrite-bom = { module = "org.openrewrite:rewrite-bom", version.ref = "openrewrite" }
openrewrite-java = { module = "org.openrewrite:rewrite-java" }
openrewrite-maven = { module = "org.openrewrite:rewrite-maven" }
openrewrite-xml = { module = "org.openrewrite:rewrite-xml" }
openrewrite-java21 = { module = "org.openrewrite:rewrite-java-21" }

# Logging
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }

# JSON Processing
jackson-core = { module = "com.fasterxml.jackson.core:jackson-core", version.ref = "jackson" }
jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jackson" }
jackson-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }

# Template Engine
freemarker = { module = "org.freemarker:freemarker", version.ref = "freemarker" }

# HTML/XML Processing
jsoup = { module = "org.jsoup:jsoup", version.ref = "jsoup" }

# Bytecode Analysis
asm = { module = "org.ow2.asm:asm", version.ref = "asm" }

# AI/LLM
openai-java = { module = "com.theokanning.openai-gpt3-java:service", version.ref = "openai-java" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
dotenv = { module = "io.github.cdimascio:dotenv-java", version.ref = "dotenv" }

# Testing
junit-jupiter = { module = "org.junit.jupiter:junit-jupiter", version.ref = "junit" }
mockito-core = { module = "org.mockito:mockito-core", version.ref = "mockito" }
mockito-junit-jupiter = { module = "org.mockito:mockito-junit-jupiter", version.ref = "mockito" }
assertj-core = { module = "org.assertj:assertj-core", version.ref = "assertj" }
