package com.phodal.migrator.service.ai.tool.impl;

import com.phodal.migrator.service.ai.tool.Tool;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件分析工具测试
 */
@DisplayName("文件分析工具测试")
public class FileAnalysisToolTest {
    
    private FileAnalysisTool fileAnalysisTool;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        fileAnalysisTool = new FileAnalysisTool();
    }
    
    @Test
    @DisplayName("测试工具基本信息")
    void testToolBasicInfo() {
        assertEquals("file_analysis", fileAnalysisTool.getName());
        assertEquals("分析文件内容、类型、大小等信息", fileAnalysisTool.getDescription());
        
        Tool.ToolSchema schema = fileAnalysisTool.getSchema();
        assertNotNull(schema);
        assertNotNull(schema.getProperties());
        assertTrue(schema.getProperties().containsKey("file_path"));
        assertTrue(schema.getProperties().containsKey("analysis_type"));
    }
    
    @Test
    @DisplayName("测试基本文件分析")
    void testBasicFileAnalysis() throws IOException {
        // 创建测试文件
        Path testFile = tempDir.resolve("test.java");
        String content = """
            package com.example;
            
            public class Test {
                public void method() {
                    System.out.println("Hello");
                }
            }
            """;
        Files.writeString(testFile, content);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", testFile.toString());
        parameters.put("analysis_type", "basic");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertTrue(result.isSuccess());
        String resultContent = result.getContent();
        assertTrue(resultContent.contains("路径:"));
        assertTrue(resultContent.contains("类型: 文件"));
        assertTrue(resultContent.contains("大小:"));
        assertTrue(resultContent.contains("扩展名: java"));
    }
    
    @Test
    @DisplayName("测试内容分析")
    void testContentAnalysis() throws IOException {
        // 创建 Java 测试文件
        Path testFile = tempDir.resolve("TestController.java");
        String content = """
            package com.example.controller;
            
            import org.springframework.web.bind.annotation.RestController;
            import org.springframework.web.bind.annotation.GetMapping;
            
            @RestController
            public class TestController {
                
                @GetMapping("/test")
                public String test() {
                    return "Hello World";
                }
            }
            """;
        Files.writeString(testFile, content);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", testFile.toString());
        parameters.put("analysis_type", "content");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertTrue(result.isSuccess());
        String resultContent = result.getContent();
        assertTrue(resultContent.contains("文件类型: Java 源代码"));
        assertTrue(resultContent.contains("行数:"));
        assertTrue(resultContent.contains("字符数:"));
        assertTrue(resultContent.contains("包含 Spring MVC 控制器"));
    }
    
    @Test
    @DisplayName("测试目录结构分析")
    void testStructureAnalysis() throws IOException {
        // 创建目录结构
        Path subDir = tempDir.resolve("subdir");
        Files.createDirectory(subDir);
        Files.createFile(tempDir.resolve("file1.txt"));
        Files.createFile(subDir.resolve("file2.txt"));
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", tempDir.toString());
        parameters.put("analysis_type", "structure");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertTrue(result.isSuccess());
        String resultContent = result.getContent();
        assertTrue(resultContent.contains("目录结构分析:"));
        assertTrue(resultContent.contains("file1.txt"));
        assertTrue(resultContent.contains("subdir"));
    }
    
    @Test
    @DisplayName("测试 Java 导入分析")
    void testJavaImportsAnalysis() throws IOException {
        Path testFile = tempDir.resolve("Test.java");
        String content = """
            package com.example;
            
            import java.util.List;
            import java.util.ArrayList;
            import org.springframework.stereotype.Service;
            import javax.persistence.Entity;
            
            @Service
            @Entity
            public class Test {
                private List<String> items = new ArrayList<>();
            }
            """;
        Files.writeString(testFile, content);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", testFile.toString());
        parameters.put("analysis_type", "dependencies");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertTrue(result.isSuccess());
        String resultContent = result.getContent();
        assertTrue(resultContent.contains("Java 导入分析:"));
        assertTrue(resultContent.contains("导入语句数量:"));
        assertTrue(resultContent.contains("使用 Spring Framework"));
        assertTrue(resultContent.contains("使用 Java EE/Jakarta EE"));
    }
    
    @Test
    @DisplayName("测试文件不存在的情况")
    void testNonExistentFile() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", "/non/existent/file.txt");
        parameters.put("analysis_type", "basic");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertFalse(result.isSuccess());
        assertTrue(result.getError().contains("文件不存在"));
    }
    
    @Test
    @DisplayName("测试空文件路径")
    void testEmptyFilePath() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", "");
        parameters.put("analysis_type", "basic");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertFalse(result.isSuccess());
        assertTrue(result.getError().contains("文件路径不能为空"));
    }
    
    @Test
    @DisplayName("测试不支持的分析类型")
    void testUnsupportedAnalysisType() throws IOException {
        Path testFile = tempDir.resolve("test.txt");
        Files.writeString(testFile, "test content");
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", testFile.toString());
        parameters.put("analysis_type", "unsupported");
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertFalse(result.isSuccess());
        assertTrue(result.getError().contains("不支持的分析类型"));
    }
    
    @Test
    @DisplayName("测试默认分析类型")
    void testDefaultAnalysisType() throws IOException {
        Path testFile = tempDir.resolve("test.txt");
        Files.writeString(testFile, "test content");
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("file_path", testFile.toString());
        // 不设置 analysis_type，应该使用默认值 "basic"
        
        Tool.ToolResult result = fileAnalysisTool.execute(parameters);
        
        assertTrue(result.isSuccess());
        assertTrue(result.getContent().contains("类型: 文件"));
    }
}
