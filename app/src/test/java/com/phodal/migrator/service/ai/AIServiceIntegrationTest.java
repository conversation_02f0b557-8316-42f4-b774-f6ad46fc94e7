package com.phodal.migrator.service.ai;

import com.phodal.migrator.config.AIProviderConfig;
import com.phodal.migrator.service.ai.tool.ToolRegistry;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AI 服务集成测试
 * 需要真实的 API 密钥才能运行
 */
@DisplayName("AI 服务集成测试")
public class AIServiceIntegrationTest {
    
    @Test
    @DisplayName("测试 AI 服务工厂")
    void testAIServiceFactory() {
        // 测试工厂方法不抛出异常
        assertDoesNotThrow(() -> {
            String info = AIServiceFactory.getAvailableProvidersInfo();
            assertNotNull(info);
            assertTrue(info.contains("可用的 AI 提供商"));
        });
        
        // 测试工具注册表创建
        ToolRegistry registry = AIServiceFactory.createDefaultToolRegistry();
        assertNotNull(registry);
        assertTrue(registry.size() > 0);
        assertTrue(registry.hasTool("file_analysis"));
        assertTrue(registry.hasTool("code_generation"));
    }
    
    @Test
    @DisplayName("测试提供商可用性检查")
    void testProviderAvailability() {
        // 测试所有提供商的可用性检查方法
        for (AIProviderConfig.ProviderType type : AIProviderConfig.ProviderType.values()) {
            assertDoesNotThrow(() -> {
                boolean available = AIServiceFactory.isProviderAvailable(type);
                // 不断言具体值，因为依赖于环境变量
            });
        }
    }
    
    @Test
    @EnabledIfEnvironmentVariable(named = "GLM_API_KEY", matches = ".+")
    @DisplayName("测试 ChatGLM 服务创建")
    void testChatGLMServiceCreation() {
        assertDoesNotThrow(() -> {
            AIService service = AIServiceFactory.createAIService(AIProviderConfig.ProviderType.CHATGLM);
            assertNotNull(service);
        });
    }
    
    @Test
    @EnabledIfEnvironmentVariable(named = "DEEPSEEK_TOKEN", matches = ".+")
    @DisplayName("测试 DeepSeek 服务创建")
    void testDeepSeekServiceCreation() {
        assertDoesNotThrow(() -> {
            AIService service = AIServiceFactory.createAIService(AIProviderConfig.ProviderType.DEEPSEEK);
            assertNotNull(service);
        });
    }
    
    @Test
    @EnabledIfEnvironmentVariable(named = "OPENAI_API_KEY", matches = ".+")
    @DisplayName("测试 OpenAI 服务创建")
    void testOpenAIServiceCreation() {
        assertDoesNotThrow(() -> {
            AIService service = AIServiceFactory.createAIService(AIProviderConfig.ProviderType.OPENAI);
            assertNotNull(service);
        });
    }
    
    @Test
    @DisplayName("测试模拟 AI 服务")
    void testMockAIService() {
        // 创建一个模拟的配置进行测试
        AIProviderConfig.ProviderConfig mockConfig = new AIProviderConfig.ProviderConfig(
            AIProviderConfig.ProviderType.OPENAI,
            "mock-api-key",
            "http://localhost:8080",
            "mock-model"
        );
        
        ToolRegistry toolRegistry = AIServiceFactory.createDefaultToolRegistry();
        
        // 测试服务创建不抛出异常（虽然实际调用会失败）
        assertDoesNotThrow(() -> {
            OpenAICompatibleAIService service = new OpenAICompatibleAIService(mockConfig, toolRegistry);
            assertNotNull(service);
        });
    }
    
    @Test
    @DisplayName("测试 AI 响应类")
    void testAIResponse() {
        // 测试成功响应
        AIService.AIResponse successResponse = new AIService.AIResponse("test content", true, null);
        assertTrue(successResponse.isSuccess());
        assertEquals("test content", successResponse.getContent());
        assertNull(successResponse.getErrorMessage());
        
        // 测试失败响应
        AIService.AIResponse errorResponse = new AIService.AIResponse(null, false, "test error");
        assertFalse(errorResponse.isSuccess());
        assertNull(errorResponse.getContent());
        assertEquals("test error", errorResponse.getErrorMessage());
    }
    
    @Test
    @DisplayName("测试迁移请求类")
    void testMigrationRequest() {
        AIService.MigrationRequest request = new AIService.MigrationRequest(
            "source code",
            "context info",
            "migration type"
        );
        
        assertEquals("source code", request.getSourceCode());
        assertEquals("context info", request.getContext());
        assertEquals("migration type", request.getMigrationType());
    }
    
    @Test
    @DisplayName("测试编译错误类")
    void testCompilationError() {
        AIService.CompilationError error = new AIService.CompilationError(
            "Test.java",
            10,
            "Compilation error message",
            "source code"
        );
        
        assertEquals("Test.java", error.getFileName());
        assertEquals(10, error.getLineNumber());
        assertEquals("Compilation error message", error.getErrorMessage());
        assertEquals("source code", error.getSourceCode());
    }
    
    @Test
    @DisplayName("测试代码修复建议类")
    void testCodeFixSuggestion() {
        AIService.CodeFixSuggestion suggestion = new AIService.CodeFixSuggestion(
            "fixed code",
            "explanation",
            java.util.Arrays.asList("step1", "step2")
        );
        
        assertEquals("fixed code", suggestion.getFixedCode());
        assertEquals("explanation", suggestion.getExplanation());
        assertEquals(2, suggestion.getSteps().size());
        assertTrue(suggestion.getSteps().contains("step1"));
        assertTrue(suggestion.getSteps().contains("step2"));
    }
    
    @Test
    @DisplayName("测试依赖推荐类")
    void testDependencyRecommendation() {
        java.util.Map<String, String> relatedDeps = new java.util.HashMap<>();
        relatedDeps.put("dep1", "1.0.0");
        
        AIService.DependencyRecommendation recommendation = new AIService.DependencyRecommendation(
            "3.0.0",
            java.util.Arrays.asList("issue1"),
            relatedDeps,
            java.util.Arrays.asList("config1")
        );
        
        assertEquals("3.0.0", recommendation.getRecommendedVersion());
        assertEquals(1, recommendation.getCompatibilityIssues().size());
        assertEquals(1, recommendation.getRelatedDependencies().size());
        assertEquals(1, recommendation.getConfigChanges().size());
    }
}
