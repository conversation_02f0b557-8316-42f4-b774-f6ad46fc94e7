package com.phodal.migrator.service.ai.tool;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具注册表测试
 */
@DisplayName("工具注册表测试")
public class ToolRegistryTest {
    
    private ToolRegistry toolRegistry;
    private TestTool testTool;
    
    @BeforeEach
    void setUp() {
        toolRegistry = new ToolRegistry();
        testTool = new TestTool();
    }
    
    @Test
    @DisplayName("测试工具注册")
    void testRegisterTool() {
        toolRegistry.registerTool(testTool);
        
        assertTrue(toolRegistry.hasTool("test_tool"));
        assertEquals(1, toolRegistry.size());
        assertTrue(toolRegistry.getToolNames().contains("test_tool"));
    }
    
    @Test
    @DisplayName("测试获取工具")
    void testGetTool() {
        toolRegistry.registerTool(testTool);
        
        assertTrue(toolRegistry.getTool("test_tool").isPresent());
        assertEquals(testTool, toolRegistry.getTool("test_tool").get());
        
        assertFalse(toolRegistry.getTool("non_existent_tool").isPresent());
    }
    
    @Test
    @DisplayName("测试执行工具")
    void testExecuteTool() {
        toolRegistry.registerTool(testTool);
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("input", "test");
        
        Tool.ToolResult result = toolRegistry.executeTool("test_tool", parameters);
        
        assertTrue(result.isSuccess());
        assertEquals("Test result: test", result.getContent());
    }
    
    @Test
    @DisplayName("测试执行不存在的工具")
    void testExecuteNonExistentTool() {
        Map<String, Object> parameters = new HashMap<>();
        
        Tool.ToolResult result = toolRegistry.executeTool("non_existent_tool", parameters);
        
        assertFalse(result.isSuccess());
        assertTrue(result.getError().contains("未找到工具"));
    }
    
    @Test
    @DisplayName("测试移除工具")
    void testRemoveTool() {
        toolRegistry.registerTool(testTool);
        assertTrue(toolRegistry.hasTool("test_tool"));
        
        toolRegistry.removeTool("test_tool");
        assertFalse(toolRegistry.hasTool("test_tool"));
        assertEquals(0, toolRegistry.size());
    }
    
    @Test
    @DisplayName("测试清空工具")
    void testClearTools() {
        toolRegistry.registerTool(testTool);
        toolRegistry.registerTool(new AnotherTestTool());
        assertEquals(2, toolRegistry.size());
        
        toolRegistry.clear();
        assertEquals(0, toolRegistry.size());
        assertTrue(toolRegistry.getToolNames().isEmpty());
    }
    
    @Test
    @DisplayName("测试获取所有工具")
    void testGetAllTools() {
        AnotherTestTool anotherTool = new AnotherTestTool();
        
        toolRegistry.registerTool(testTool);
        toolRegistry.registerTool(anotherTool);
        
        assertEquals(2, toolRegistry.getAllTools().size());
        assertTrue(toolRegistry.getAllTools().contains(testTool));
        assertTrue(toolRegistry.getAllTools().contains(anotherTool));
    }
    
    /**
     * 测试工具实现
     */
    private static class TestTool implements Tool {
        
        @Override
        public String getName() {
            return "test_tool";
        }
        
        @Override
        public String getDescription() {
            return "A test tool";
        }
        
        @Override
        public ToolSchema getSchema() {
            Map<String, PropertySchema> properties = new HashMap<>();
            properties.put("input", new PropertySchema("string", "Test input"));
            return new ToolSchema(properties, new String[]{"input"});
        }
        
        @Override
        public ToolResult execute(Map<String, Object> parameters) {
            String input = (String) parameters.get("input");
            if (input == null) {
                return ToolResult.error("Missing input parameter");
            }
            return ToolResult.success("Test result: " + input);
        }
    }
    
    /**
     * 另一个测试工具实现
     */
    private static class AnotherTestTool implements Tool {
        
        @Override
        public String getName() {
            return "another_test_tool";
        }
        
        @Override
        public String getDescription() {
            return "Another test tool";
        }
        
        @Override
        public ToolSchema getSchema() {
            return new ToolSchema(new HashMap<>(), new String[0]);
        }
        
        @Override
        public ToolResult execute(Map<String, Object> parameters) {
            return ToolResult.success("Another test result");
        }
    }
}
