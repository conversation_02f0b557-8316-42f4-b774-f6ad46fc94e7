package com.phodal.migrator.config;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * AI 提供商配置测试
 */
@DisplayName("AI 提供商配置测试")
public class AIProviderConfigTest {
    
    @Test
    @DisplayName("测试提供商类型枚举")
    void testProviderTypes() {
        // 测试 DeepSeek 配置
        AIProviderConfig.ProviderType deepseek = AIProviderConfig.ProviderType.DEEPSEEK;
        assertEquals("DeepSeek", deepseek.getDisplayName());
        assertEquals("https://api.deepseek.com/v1", deepseek.getBaseUrl());
        assertEquals("deepseek-chat", deepseek.getDefaultModel());
        
        // 测试 ChatGLM 配置
        AIProviderConfig.ProviderType chatglm = AIProviderConfig.ProviderType.CHATGLM;
        assertEquals("ChatGLM", chatglm.getDisplayName());
        assertEquals("https://open.bigmodel.cn/api/paas/v4", chatglm.getBaseUrl());
        assertEquals("glm-4-air", chatglm.getDefaultModel());
        
        // 测试 OpenAI 配置
        AIProviderConfig.ProviderType openai = AIProviderConfig.ProviderType.OPENAI;
        assertEquals("OpenAI", openai.getDisplayName());
        assertEquals("https://api.openai.com/v1", openai.getBaseUrl());
        assertEquals("gpt-3.5-turbo", openai.getDefaultModel());
    }
    
    @Test
    @DisplayName("测试提供商配置创建")
    void testProviderConfigCreation() {
        AIProviderConfig.ProviderConfig config = new AIProviderConfig.ProviderConfig(
            AIProviderConfig.ProviderType.DEEPSEEK,
            "test-api-key",
            "https://test.api.com",
            "test-model"
        );
        
        assertEquals(AIProviderConfig.ProviderType.DEEPSEEK, config.getType());
        assertEquals("test-api-key", config.getApiKey());
        assertEquals("https://test.api.com", config.getBaseUrl());
        assertEquals("test-model", config.getModel());
        assertEquals("DeepSeek", config.getDisplayName());
    }
    
    @Test
    @DisplayName("测试提供商可用性检查")
    void testProviderAvailability() {
        // 注意：这个测试依赖于环境变量，在 CI/CD 环境中可能需要模拟
        // 这里只测试方法不抛出异常
        assertDoesNotThrow(() -> {
            AIProviderConfig.isProviderAvailable(AIProviderConfig.ProviderType.DEEPSEEK);
            AIProviderConfig.isProviderAvailable(AIProviderConfig.ProviderType.CHATGLM);
            AIProviderConfig.isProviderAvailable(AIProviderConfig.ProviderType.OPENAI);
        });
    }
    
    @Test
    @DisplayName("测试获取不存在的提供商配置")
    void testGetNonExistentProviderConfig() {
        // 由于我们无法保证环境变量的存在，这里测试异常处理
        assertDoesNotThrow(() -> {
            try {
                AIProviderConfig.getProviderConfig(AIProviderConfig.ProviderType.OPENAI);
            } catch (IllegalStateException e) {
                // 预期的异常，当 API 密钥未设置时
                assertTrue(e.getMessage().contains("未设置"));
            }
        });
    }
}
