package com.phodal.migrator.core;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * MigrationOptions 单元测试
 */
class MigrationOptionsTest {
    
    @Test
    void shouldCreateDefaultOptions() {
        // When
        MigrationOptions options = MigrationOptions.builder().build();
        
        // Then
        assertThat(options.isDryRun()).isFalse();
        assertThat(options.isVerbose()).isFalse();
        assertThat(options.isEnableAiFix()).isFalse();
        assertThat(options.getMaxBuildAttempts()).isEqualTo(3);
        assertThat(options.getSkipSteps()).isEmpty();
        assertThat(options.getBuildCommand()).isNull();
        assertThat(options.getReportFormats()).containsExactly("markdown");
        assertThat(options.getAiProvider()).isEqualTo("phodal");
    }
    
    @Test
    void shouldCreateCustomOptions() {
        // When
        MigrationOptions options = MigrationOptions.builder()
                .dryRun(true)
                .verbose(true)
                .enableAiFix(true)
                .maxBuildAttempts(5)
                .skipSteps(Set.of("test", "runtime"))
                .buildCommand("mvn clean compile")
                .reportFormats(List.of("json", "html"))
                .aiProvider("openai")
                .build();
        
        // Then
        assertThat(options.isDryRun()).isTrue();
        assertThat(options.isVerbose()).isTrue();
        assertThat(options.isEnableAiFix()).isTrue();
        assertThat(options.getMaxBuildAttempts()).isEqualTo(5);
        assertThat(options.getSkipSteps()).containsExactlyInAnyOrder("test", "runtime");
        assertThat(options.getBuildCommand()).isEqualTo("mvn clean compile");
        assertThat(options.getReportFormats()).containsExactly("json", "html");
        assertThat(options.getAiProvider()).isEqualTo("openai");
    }
    
    @Test
    void shouldCheckSkipSteps() {
        // Given
        MigrationOptions options = MigrationOptions.builder()
                .skipSteps(Set.of("config", "test"))
                .build();
        
        // Then
        assertThat(options.shouldSkipStep("config")).isTrue();
        assertThat(options.shouldSkipStep("test")).isTrue();
        assertThat(options.shouldSkipStep("build")).isFalse();
    }
}
