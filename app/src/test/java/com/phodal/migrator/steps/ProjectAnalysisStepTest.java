package com.phodal.migrator.steps;

import com.phodal.migrator.application.ProjectAnalysisStep;
import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.MigrationOptions;
import com.phodal.migrator.core.StepResult;
import com.phodal.migrator.core.ValidationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * ProjectAnalysisStep 单元测试
 */
class ProjectAnalysisStepTest {
    
    @TempDir
    Path tempDir;
    
    private ProjectAnalysisStep step;
    private Path testProjectPath;
    private MigrationContext context;
    
    @BeforeEach
    void setUp() throws IOException {
        step = new ProjectAnalysisStep();
        testProjectPath = tempDir.resolve("test-project");
        Files.createDirectories(testProjectPath);
        
        MigrationOptions options = MigrationOptions.builder()
                .dryRun(true)
                .build();
        
        context = new MigrationContext(testProjectPath, testProjectPath, options);
    }
    
    @Test
    void shouldHaveCorrectMetadata() {
        assertThat(step.getName()).isEqualTo("project_analysis");
        assertThat(step.getDescription()).isEqualTo("分析项目结构，检测版本信息，创建备份");
        assertThat(step.isRequired()).isTrue();
        assertThat(step.getEstimatedDurationSeconds()).isEqualTo(30);
    }
    
    @Test
    void shouldValidateExistingProject() {
        // When
        ValidationResult result = step.validate(context);
        
        // Then
        assertThat(result.isValid()).isTrue();
        assertThat(result.getErrors()).isEmpty();
    }
    
    @Test
    void shouldFailValidationForNonExistentProject() {
        // Given
        Path nonExistentPath = tempDir.resolve("non-existent");
        MigrationOptions options = MigrationOptions.builder().build();
        MigrationContext invalidContext = new MigrationContext(nonExistentPath, nonExistentPath, options);
        
        // When
        ValidationResult result = step.validate(invalidContext);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).isNotEmpty();
        assertThat(result.getErrors().get(0)).contains("项目路径不存在");
    }
    
    @Test
    void shouldExecuteSuccessfullyInDryRunMode() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        // When
        StepResult result = step.execute(context);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getMessage()).isEqualTo("项目分析完成");
        assertThat(result.getDetails()).containsKey("analysis_result");
        
        // 在预览模式下不应该创建备份
        assertThat(result.getDetails().get("analysis_result")).isInstanceOf(java.util.Map.class);
    }
    
    @Test
    void shouldDetectProjectStructure() throws IOException {
        // Given
        Files.createFile(testProjectPath.resolve("pom.xml"));
        
        // 创建一些测试文件
        Path srcMain = testProjectPath.resolve("src/main/java");
        Files.createDirectories(srcMain);
        Files.createFile(srcMain.resolve("Application.java"));
        
        Path srcTest = testProjectPath.resolve("src/test/java");
        Files.createDirectories(srcTest);
        Files.createFile(srcTest.resolve("ApplicationTest.java"));
        
        // When
        StepResult result = step.execute(context);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(context.getCurrentSpringBootVersion()).isEqualTo("2.7.0");
        assertThat(context.getCurrentJavaVersion()).isEqualTo("8");
        assertThat(context.getTargetSpringBootVersion()).isEqualTo("3.2.0");
        assertThat(context.getTargetJavaVersion()).isEqualTo("21");
    }
}
