package com.phodal.migrator.example;

import com.phodal.migrator.config.AIProviderConfig;
import com.phodal.migrator.service.ai.AIService;
import com.phodal.migrator.service.ai.AIServiceFactory;
import com.phodal.migrator.service.ai.tool.Tool;
import com.phodal.migrator.service.ai.tool.ToolRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * AI 服务使用示例
 * 演示如何使用 DeepSeek 和 ChatGLM 进行代码分析和生成
 */
public class AIServiceExample {
    
    private static final Logger logger = LoggerFactory.getLogger(AIServiceExample.class);
    
    public static void main(String[] args) {
        try {
            // 检查 AI 服务可用性
            if (!AIServiceFactory.isAIServiceAvailable()) {
                logger.error("没有可用的 AI 服务，请检查 .env 文件中的 API 密钥配置");
                System.out.println(AIServiceFactory.getAvailableProvidersInfo());
                return;
            }
            
            // 创建 AI 服务
            AIService aiService = AIServiceFactory.createDefaultAIService();
            logger.info("AI 服务创建成功");
            
            // 示例1：代码迁移建议
            demonstrateMigrationSuggestion(aiService);
            
            // 示例2：代码修复建议
            demonstrateCodeFix(aiService);
            
            // 示例3：依赖升级建议
            demonstrateDependencyRecommendation(aiService);
            
            // 示例4：工具调用
            demonstrateToolUsage();
            
        } catch (Exception e) {
            logger.error("AI 服务示例执行失败", e);
        }
    }
    
    private static void demonstrateMigrationSuggestion(AIService aiService) {
        System.out.println("\n=== 代码迁移建议示例 ===");
        
        String sourceCode = """
            @RestController
            public class UserController {
                
                @Autowired
                private UserService userService;
                
                @RequestMapping(value = "/users", method = RequestMethod.GET)
                public List<User> getUsers() {
                    return userService.findAll();
                }
                
                @RequestMapping(value = "/users/{id}", method = RequestMethod.GET)
                public User getUser(@PathVariable Long id) {
                    return userService.findById(id);
                }
            }
            """;
        
        AIService.MigrationRequest request = new AIService.MigrationRequest(
            sourceCode,
            "Spring Boot 2.x 到 3.x 迁移",
            "Spring Boot 升级"
        );
        
        AIService.AIResponse response = aiService.generateMigrationSuggestion(request);
        
        if (response.isSuccess()) {
            System.out.println("迁移建议：");
            System.out.println(response.getContent());
        } else {
            System.out.println("生成迁移建议失败：" + response.getErrorMessage());
        }
    }
    
    private static void demonstrateCodeFix(AIService aiService) {
        System.out.println("\n=== 代码修复建议示例 ===");
        
        String errorCode = """
            import javax.persistence.Entity;
            import javax.persistence.Id;
            
            @Entity
            public class User {
                @Id
                private Long id;
                private String name;
                
                // getters and setters
            }
            """;
        
        AIService.CompilationError error = new AIService.CompilationError(
            "User.java",
            1,
            "Package javax.persistence does not exist",
            errorCode
        );
        
        AIService.CodeFixSuggestion suggestion = aiService.suggestCodeFix(error);
        
        System.out.println("修复建议：");
        System.out.println("解释：" + suggestion.getExplanation());
        if (suggestion.getFixedCode() != null) {
            System.out.println("修复后的代码：");
            System.out.println(suggestion.getFixedCode());
        }
        System.out.println("修复步骤：");
        suggestion.getSteps().forEach(step -> System.out.println("- " + step));
    }
    
    private static void demonstrateDependencyRecommendation(AIService aiService) {
        System.out.println("\n=== 依赖升级建议示例 ===");
        
        AIService.DependencyRecommendation recommendation = aiService.recommendDependencyUpgrade(
            "spring-boot-starter-web:2.7.0",
            "3.2.0"
        );
        
        System.out.println("推荐版本：" + recommendation.getRecommendedVersion());
        System.out.println("兼容性问题：");
        recommendation.getCompatibilityIssues().forEach(issue -> System.out.println("- " + issue));
        System.out.println("配置更改：");
        recommendation.getConfigChanges().forEach(change -> System.out.println("- " + change));
    }
    
    private static void demonstrateToolUsage() {
        System.out.println("\n=== 工具调用示例 ===");
        
        // 创建工具注册表
        ToolRegistry toolRegistry = AIServiceFactory.createDefaultToolRegistry();
        
        // 示例1：文件分析工具
        Map<String, Object> fileAnalysisParams = new HashMap<>();
        fileAnalysisParams.put("file_path", "app/src/main/java/com/phodal/migrator/service/ai/AIService.java");
        fileAnalysisParams.put("analysis_type", "basic");
        
        Tool.ToolResult fileResult = toolRegistry.executeTool("file_analysis", fileAnalysisParams);
        System.out.println("文件分析结果：");
        System.out.println(fileResult.toString());
        
        // 示例2：代码生成工具
        Map<String, Object> codeGenParams = new HashMap<>();
        codeGenParams.put("template_type", "controller");
        codeGenParams.put("class_name", "Product");
        codeGenParams.put("package_name", "com.example.demo");
        
        Tool.ToolResult codeResult = toolRegistry.executeTool("code_generation", codeGenParams);
        System.out.println("\n代码生成结果：");
        System.out.println(codeResult.toString());
    }
    
    /**
     * 演示如何使用特定的 AI 提供商
     */
    public static void demonstrateSpecificProvider() {
        System.out.println("\n=== 特定提供商示例 ===");
        
        // 尝试使用 DeepSeek
        if (AIServiceFactory.isProviderAvailable(AIProviderConfig.ProviderType.DEEPSEEK)) {
            System.out.println("使用 DeepSeek:");
            AIService deepseekService = AIServiceFactory.createAIService(AIProviderConfig.ProviderType.DEEPSEEK);
            // 使用 deepseekService...
        }
        
        // 尝试使用 ChatGLM
        if (AIServiceFactory.isProviderAvailable(AIProviderConfig.ProviderType.CHATGLM)) {
            System.out.println("使用 ChatGLM:");
            AIService chatglmService = AIServiceFactory.createAIService(AIProviderConfig.ProviderType.CHATGLM);
            // 使用 chatglmService...
        }
    }
}
