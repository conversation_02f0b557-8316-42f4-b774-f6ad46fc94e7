package com.phodal.migrator;

import com.phodal.migrator.application.*;
import com.phodal.migrator.core.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Spring Boot 2.x → 3.x + JDK 8 → 21 迁移工具主编排器
 * 
 * 实现8步迁移流程：
 * 1. 项目分析与备份
 * 2. 配置文件迁移
 * 3. 依赖升级
 * 4. 代码转换
 * 5. 构建修复
 * 6. 测试执行
 * 7. 运行时验证
 * 8. 报告生成
 */
public class JavaAutoMigrator {
    private static final Logger logger = LoggerFactory.getLogger(JavaAutoMigrator.class);
    
    private final MigrationContext context;
    private final List<MigrationStep> migrationSteps;
    private final MigrationStats stats;
    
    public JavaAutoMigrator(Path projectPath, Path targetPath, MigrationOptions options) {
        this.context = new MigrationContext(projectPath, targetPath, options);
        this.stats = new MigrationStats();
        this.migrationSteps = initializeMigrationSteps();

        // 初始化时检测项目类型
        ProjectType projectType = detectProjectType();
        context.setProjectType(projectType);
    }

    public JavaAutoMigrator(Path projectPath, MigrationOptions options) {
        this(projectPath, projectPath, options);
    }
    
    /**
     * 执行完整的迁移流程
     */
    public MigrationResult migrate() {
        logger.info("🚀 开始 Spring Boot 2.x → 3.x + JDK 8 → 21 迁移...");
        logger.info("项目路径: {}", context.getProjectPath());
        logger.info("目标路径: {}", context.getTargetPath());
        logger.info("预览模式: {}", context.isDryRun() ? "是" : "否");
        
        stats.setStartTime(Instant.now());
        List<StepResult> stepResults = new ArrayList<>();
        
        try {
            // 验证项目环境
            validateProject();
            
            // 执行8个迁移步骤
            for (int i = 0; i < migrationSteps.size(); i++) {
                MigrationStep step = migrationSteps.get(i);
                
                // 检查是否跳过此步骤
                if (context.getOptions().shouldSkipStep(step.getName())) {
                    logger.info("⏭️  跳过步骤 {}/{}: {}", i + 1, migrationSteps.size(), step.getName());
                    continue;
                }
                
                logger.info("📋 步骤 {}/{}: {}", i + 1, migrationSteps.size(), step.getName());
                logger.info("   描述: {}", step.getDescription());
                
                // 验证步骤前置条件
                ValidationResult validation = step.validate(context);
                if (!validation.isValid()) {
                    String errorMsg = "步骤前置条件验证失败: " + String.join(", ", validation.getErrors());
                    logger.error("❌ {}", errorMsg);
                    
                    if (step.isRequired()) {
                        throw new MigrationException(errorMsg);
                    } else {
                        logger.warn("⚠️  跳过可选步骤: {}", step.getName());
                        continue;
                    }
                }
                
                // 执行步骤
                Instant stepStart = Instant.now();
                StepResult result = step.execute(context);
                Instant stepEnd = Instant.now();
                
                stepResults.add(result);
                stats.addStepResult(step.getName(), result);
                
                // 记录步骤结果
                if (result.isSuccess()) {
                    if (result.isWarning()) {
                        logger.warn("⚠️  步骤完成但有警告: {} (耗时: {}ms)", 
                                   result.getMessage(), result.getDuration().toMillis());
                    } else {
                        logger.info("✅ 步骤成功: {} (耗时: {}ms)", 
                                   result.getMessage(), result.getDuration().toMillis());
                    }
                } else {
                    logger.error("❌ 步骤失败: {}", result.getMessage());
                    if (result.getError() != null && context.isVerbose()) {
                        logger.error("错误详情:", result.getError());
                    }
                    
                    if (step.isRequired()) {
                        throw new MigrationException("关键步骤失败: " + step.getName(), result.getError());
                    }
                }
                
                // 更新上下文
                context.updateFromStepResult(result);
                
                // 详细输出模式下显示步骤详情
                if (context.isVerbose() && !result.getDetails().isEmpty()) {
                    logger.info("📊 步骤详情:");
                    result.getDetails().forEach((key, value) -> 
                        logger.info("   {}: {}", key, value));
                }
            }
            
            stats.setEndTime(Instant.now());
            stats.setSuccess(true);
            
            logger.info("✅ 迁移完成！总耗时: {}ms", stats.getTotalDuration().toMillis());
            
            return MigrationResult.builder()
                    .success(true)
                    .message("迁移成功完成")
                    .stats(stats)
                    .stepResults(stepResults)
                    .startTime(stats.getStartTime())
                    .endTime(stats.getEndTime())
                    .build();
                    
        } catch (Exception e) {
            stats.setEndTime(Instant.now());
            stats.setSuccess(false);
            stats.setError(e.getMessage());
            
            logger.error("❌ 迁移失败: {}", e.getMessage());
            if (context.isVerbose()) {
                logger.error("错误详情:", e);
            }
            
            return MigrationResult.builder()
                    .success(false)
                    .message("迁移失败: " + e.getMessage())
                    .stats(stats)
                    .stepResults(stepResults)
                    .startTime(stats.getStartTime())
                    .endTime(stats.getEndTime())
                    .build();
        }
    }
    
    /**
     * 初始化8个迁移步骤
     */
    private List<MigrationStep> initializeMigrationSteps() {
        return Arrays.asList(
            new ProjectAnalysisStep(),      // 1. 项目分析与备份
            new ConfigMigrationStep(),      // 2. 配置文件迁移
            new DependencyMigrationStep(),  // 3. 依赖升级
            new CodeMigrationStep(),        // 4. 代码转换
            new BuildFixStep(),             // 5. 构建修复
            new TestExecutionStep(),        // 6. 测试执行
            new RuntimeValidationStep(),    // 7. 运行时验证
            new ReportGenerationStep()      // 8. 报告生成
        );
    }
    
    /**
     * 验证项目环境
     */
    private void validateProject() {
        if (!context.getProjectPath().toFile().exists()) {
            throw new MigrationException("项目路径不存在: " + context.getProjectPath());
        }

        if (!context.getProjectPath().toFile().isDirectory()) {
            throw new MigrationException("项目路径不是目录: " + context.getProjectPath());
        }

        if (context.getProjectType() == ProjectType.UNKNOWN) {
            throw new MigrationException("无法识别项目类型，请确保项目包含 pom.xml 或 build.gradle 文件");
        }

        logger.info("📁 检测到项目类型: {}", context.getProjectType().getDisplayName());
    }
    
    /**
     * 检测项目类型
     */
    private ProjectType detectProjectType() {
        Path projectPath = context.getProjectPath();
        
        if (projectPath.resolve("pom.xml").toFile().exists()) {
            return ProjectType.MAVEN;
        }
        
        if (projectPath.resolve("build.gradle.kts").toFile().exists()) {
            return ProjectType.GRADLE_KTS;
        }
        
        if (projectPath.resolve("build.gradle").toFile().exists()) {
            return ProjectType.GRADLE;
        }
        
        return ProjectType.UNKNOWN;
    }
    
    /**
     * 获取迁移统计信息
     */
    public MigrationStats getStats() {
        return stats;
    }
    
    /**
     * 获取迁移上下文
     */
    public MigrationContext getContext() {
        return context;
    }
}
