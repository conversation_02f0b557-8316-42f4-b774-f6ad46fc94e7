package com.phodal.migrator.service.ai;

import com.phodal.migrator.config.AIProviderConfig;
import com.phodal.migrator.service.ai.tool.ToolRegistry;
import com.phodal.migrator.service.ai.tool.impl.CodeGenerationTool;
import com.phodal.migrator.service.ai.tool.impl.FileAnalysisTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI 服务工厂
 * 根据配置创建合适的 AI 服务实例
 */
public class AIServiceFactory {
    
    private static final Logger logger = LoggerFactory.getLogger(AIServiceFactory.class);
    
    /**
     * 创建默认的 AI 服务
     * 自动选择可用的提供商
     */
    public static AIService createDefaultAIService() {
        try {
            AIProviderConfig.ProviderConfig config = AIProviderConfig.getAvailableProvider();
            logger.info("使用 AI 提供商: {}", config.getDisplayName());
            
            ToolRegistry toolRegistry = createDefaultToolRegistry();
            return new OpenAICompatibleAIService(config, toolRegistry);
            
        } catch (Exception e) {
            logger.error("创建 AI 服务失败", e);
            throw new RuntimeException("无法创建 AI 服务: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建指定提供商的 AI 服务
     */
    public static AIService createAIService(AIProviderConfig.ProviderType providerType) {
        try {
            AIProviderConfig.ProviderConfig config = AIProviderConfig.getProviderConfig(providerType);
            logger.info("使用指定的 AI 提供商: {}", config.getDisplayName());
            
            ToolRegistry toolRegistry = createDefaultToolRegistry();
            return new OpenAICompatibleAIService(config, toolRegistry);
            
        } catch (Exception e) {
            logger.error("创建 AI 服务失败: {}", e.getMessage());
            throw new RuntimeException("无法创建 AI 服务: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建带有自定义工具注册表的 AI 服务
     */
    public static AIService createAIService(AIProviderConfig.ProviderType providerType, ToolRegistry toolRegistry) {
        try {
            AIProviderConfig.ProviderConfig config = AIProviderConfig.getProviderConfig(providerType);
            logger.info("使用指定的 AI 提供商: {}", config.getDisplayName());
            
            return new OpenAICompatibleAIService(config, toolRegistry);
            
        } catch (Exception e) {
            logger.error("创建 AI 服务失败: {}", e.getMessage());
            throw new RuntimeException("无法创建 AI 服务: " + e.getMessage(), e);
        }
    }
    
    /**
     * 创建默认的工具注册表
     */
    public static ToolRegistry createDefaultToolRegistry() {
        ToolRegistry registry = new ToolRegistry();
        
        // 注册默认工具
        registry.registerTool(new FileAnalysisTool());
        registry.registerTool(new CodeGenerationTool());
        
        logger.info("已注册 {} 个工具: {}", registry.size(), registry.getToolNames());
        
        return registry;
    }
    
    /**
     * 检查 AI 服务是否可用
     */
    public static boolean isAIServiceAvailable() {
        try {
            AIProviderConfig.getAvailableProvider();
            return true;
        } catch (Exception e) {
            logger.warn("AI 服务不可用: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查指定提供商是否可用
     */
    public static boolean isProviderAvailable(AIProviderConfig.ProviderType providerType) {
        return AIProviderConfig.isProviderAvailable(providerType);
    }
    
    /**
     * 获取可用的提供商信息
     */
    public static String getAvailableProvidersInfo() {
        StringBuilder info = new StringBuilder("可用的 AI 提供商:\n");
        
        for (AIProviderConfig.ProviderType type : AIProviderConfig.ProviderType.values()) {
            boolean available = AIProviderConfig.isProviderAvailable(type);
            info.append(String.format("- %s: %s\n", 
                type.getDisplayName(), 
                available ? "可用" : "不可用"));
        }
        
        return info.toString();
    }
    
    /**
     * 创建测试用的 AI 服务
     * 用于单元测试，不需要真实的 API 密钥
     */
    public static AIService createTestAIService() {
        // 创建一个模拟的配置
        AIProviderConfig.ProviderConfig mockConfig = new AIProviderConfig.ProviderConfig(
            AIProviderConfig.ProviderType.OPENAI,
            "test-api-key",
            "http://localhost:8080",
            "test-model"
        );
        
        ToolRegistry toolRegistry = createDefaultToolRegistry();
        
        // 注意：这里应该返回一个模拟的实现，而不是真实的 OpenAI 服务
        // 在实际项目中，你可能需要创建一个 MockAIService 类
        logger.warn("创建测试 AI 服务，仅用于测试目的");
        return new OpenAICompatibleAIService(mockConfig, toolRegistry);
    }
}
