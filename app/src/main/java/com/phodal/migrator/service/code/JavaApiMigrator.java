package com.phodal.migrator.service.code;

import org.jetbrains.annotations.NotNull;
import org.openrewrite.ExecutionContext;
import org.openrewrite.java.JavaIsoVisitor;
import org.openrewrite.java.tree.J;

/**
 * Java API 迁移器
 * 负责处理 javax.* 到 jakarta.* 的包名转换
 */
public class JavaApiMigrator extends JavaIsoVisitor<ExecutionContext> {
    
    @Override
    public J.@NotNull Import visitImport(J.@NotNull Import import_, @NotNull ExecutionContext ctx) {
        J.Import i = super.visitImport(import_, ctx);
        if (i.getQualid().toString().startsWith("javax.")) {
            System.out.println("Found javax import: " + i.getQualid().toString());
            // TODO: 实现具体的转换逻辑
            // 将 javax.* 转换为 jakarta.*
        }
        return i;
    }
}
