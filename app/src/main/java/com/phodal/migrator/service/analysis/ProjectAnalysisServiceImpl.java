package com.phodal.migrator.service.analysis;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.ProjectType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 项目分析服务实现
 */
public class ProjectAnalysisServiceImpl implements ProjectAnalysisService {
    private static final Logger logger = LoggerFactory.getLogger(ProjectAnalysisServiceImpl.class);
    
    @Override
    public ProjectAnalysisResult analyzeProject(Path projectPath) {
        Map<String, Object> analysisData = new HashMap<>();
        
        try {
            // 分析项目结构
            ProjectStructure structure = analyzeProjectStructure(projectPath);
            analysisData.put("project_structure", structure);
            
            // 检测项目类型
            ProjectType projectType = detectProjectType(projectPath);
            analysisData.put("project_type", projectType.getDisplayName());
            
            logger.info("项目分析完成: {}", projectPath);
            return new ProjectAnalysisResult(analysisData, true, "项目分析成功");
            
        } catch (Exception e) {
            logger.error("项目分析失败: {}", e.getMessage(), e);
            return new ProjectAnalysisResult(analysisData, false, "项目分析失败: " + e.getMessage());
        }
    }
    
    @Override
    public String detectSpringBootVersion(MigrationContext context) {
        Path projectPath = context.getProjectPath();
        
        try {
            // 检查 pom.xml 中的 Spring Boot 版本
            Path pomFile = projectPath.resolve("pom.xml");
            if (Files.exists(pomFile)) {
                return detectSpringBootVersionFromPom(pomFile);
            }
            
            // 检查 build.gradle 中的 Spring Boot 版本
            Path gradleFile = projectPath.resolve("build.gradle");
            if (Files.exists(gradleFile)) {
                return detectSpringBootVersionFromGradle(gradleFile);
            }
            
            // 检查 build.gradle.kts 中的 Spring Boot 版本
            Path gradleKtsFile = projectPath.resolve("build.gradle.kts");
            if (Files.exists(gradleKtsFile)) {
                return detectSpringBootVersionFromGradleKts(gradleKtsFile);
            }
            
        } catch (Exception e) {
            logger.warn("检测Spring Boot版本失败: {}", e.getMessage());
        }
        
        return "未知";
    }
    
    @Override
    public String detectJavaVersion(MigrationContext context) {
        Path projectPath = context.getProjectPath();
        
        try {
            // 检查 pom.xml 中的 Java 版本
            Path pomFile = projectPath.resolve("pom.xml");
            if (Files.exists(pomFile)) {
                return detectJavaVersionFromPom(pomFile);
            }
            
            // 检查 build.gradle 中的 Java 版本
            Path gradleFile = projectPath.resolve("build.gradle");
            if (Files.exists(gradleFile)) {
                return detectJavaVersionFromGradle(gradleFile);
            }
            
        } catch (Exception e) {
            logger.warn("检测Java版本失败: {}", e.getMessage());
        }
        
        return "未知";
    }
    
    @Override
    public Path createBackup(MigrationContext context) {
        Path projectPath = context.getProjectPath();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        Path backupPath = projectPath.getParent().resolve(projectPath.getFileName() + "_backup_" + timestamp);
        
        try {
            copyDirectory(projectPath, backupPath);
            logger.info("备份创建成功: {}", backupPath);
            return backupPath;
        } catch (IOException e) {
            throw new RuntimeException("创建备份失败: " + e.getMessage(), e);
        }
    }
    
    private ProjectStructure analyzeProjectStructure(Path projectPath) throws IOException {
        ProjectStructure structure = new ProjectStructure();
        
        // 统计Java文件
        try (Stream<Path> paths = Files.walk(projectPath)) {
            long javaFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith(".java"))
                .count();
            structure.setJavaFiles((int) javaFiles);
        }
        
        // 统计配置文件
        try (Stream<Path> paths = Files.walk(projectPath)) {
            long configFiles = paths
                .filter(Files::isRegularFile)
                .filter(path -> {
                    String fileName = path.getFileName().toString();
                    return fileName.endsWith(".properties") || 
                           fileName.endsWith(".yml") || 
                           fileName.endsWith(".yaml") ||
                           fileName.equals("pom.xml") ||
                           fileName.startsWith("build.gradle");
                })
                .count();
            structure.setConfigFiles((int) configFiles);
        }
        
        // 检测Spring Boot特性
        structure.setHasSpringBoot(hasSpringBootDependency(projectPath));
        
        return structure;
    }
    
    private ProjectType detectProjectType(Path projectPath) {
        if (Files.exists(projectPath.resolve("pom.xml"))) {
            return ProjectType.MAVEN;
        } else if (Files.exists(projectPath.resolve("build.gradle.kts"))) {
            return ProjectType.GRADLE_KTS;
        } else if (Files.exists(projectPath.resolve("build.gradle"))) {
            return ProjectType.GRADLE;
        } else {
            return ProjectType.UNKNOWN;
        }
    }
    
    private String detectSpringBootVersionFromPom(Path pomFile) throws IOException {
        // TODO: 实现从pom.xml解析Spring Boot版本
        return "2.7.0"; // 临时返回值
    }
    
    private String detectSpringBootVersionFromGradle(Path gradleFile) throws IOException {
        // TODO: 实现从build.gradle解析Spring Boot版本
        return "2.7.0"; // 临时返回值
    }
    
    private String detectSpringBootVersionFromGradleKts(Path gradleKtsFile) throws IOException {
        // TODO: 实现从build.gradle.kts解析Spring Boot版本
        return "2.7.0"; // 临时返回值
    }
    
    private String detectJavaVersionFromPom(Path pomFile) throws IOException {
        // TODO: 实现从pom.xml解析Java版本
        return "8"; // 临时返回值
    }
    
    private String detectJavaVersionFromGradle(Path gradleFile) throws IOException {
        // TODO: 实现从build.gradle解析Java版本
        return "8"; // 临时返回值
    }
    
    private boolean hasSpringBootDependency(Path projectPath) {
        // TODO: 实现Spring Boot依赖检测
        return true; // 临时返回值
    }
    
    private void copyDirectory(Path source, Path target) throws IOException {
        try (Stream<Path> paths = Files.walk(source)) {
            paths.forEach(sourcePath -> {
                try {
                    Path targetPath = target.resolve(source.relativize(sourcePath));
                    if (Files.isDirectory(sourcePath)) {
                        Files.createDirectories(targetPath);
                    } else {
                        Files.copy(sourcePath, targetPath);
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }
    }
    
    /**
     * 项目结构信息
     */
    public static class ProjectStructure {
        private int javaFiles;
        private int configFiles;
        private boolean hasSpringBoot;
        
        public int getJavaFiles() { return javaFiles; }
        public void setJavaFiles(int javaFiles) { this.javaFiles = javaFiles; }
        
        public int getConfigFiles() { return configFiles; }
        public void setConfigFiles(int configFiles) { this.configFiles = configFiles; }
        
        public boolean isHasSpringBoot() { return hasSpringBoot; }
        public void setHasSpringBoot(boolean hasSpringBoot) { this.hasSpringBoot = hasSpringBoot; }
    }
}
