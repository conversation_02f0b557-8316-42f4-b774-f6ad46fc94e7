package com.phodal.migrator.service.dependency;

import org.openrewrite.ExecutionContext;
import org.openrewrite.maven.MavenIsoVisitor;
import org.openrewrite.xml.tree.Xml;

import java.util.Optional;

/**
 * Maven 依赖升级器
 * 负责升级 Maven 项目中的依赖版本
 */
public class MavenDependencyUpgrader extends MavenIsoVisitor<ExecutionContext> {
    private final String groupId;
    private final String artifactId;
    private final String newVersion;

    public MavenDependencyUpgrader(String groupId, String artifactId, String newVersion) {
        this.groupId = groupId;
        this.artifactId = artifactId;
        this.newVersion = newVersion;
    }

    @Override
    public Xml.Tag visitTag(Xml.Tag tag, ExecutionContext ctx) {
        if ("dependency".equals(tag.getName())) {
            Optional<String> groupIdTag = tag.getChildValue("groupId");
            Optional<String> artifactIdTag = tag.getChildValue("artifactId");

            if (groupIdTag.isPresent() && groupIdTag.get().equals(groupId) &&
                artifactIdTag.isPresent() && artifactIdTag.get().equals(artifactId)) {

                Optional<Xml.Tag> versionTag = tag.getChild("version");
                if (versionTag.isPresent()) {
                    Xml.Tag vt = versionTag.get();
                    return tag.withContent(tag.getContent().stream()
                        .map(content -> content == vt ? vt.withValue(newVersion) : content)
                        .collect(java.util.stream.Collectors.toList()));
                }
            }
        }
        return super.visitTag(tag, ctx);
    }
}
