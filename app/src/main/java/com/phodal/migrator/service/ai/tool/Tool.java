package com.phodal.migrator.service.ai.tool;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * 工具定义接口
 * 基于 MCP (Model Context Protocol) 标准
 */
public interface Tool {
    
    /**
     * 获取工具名称
     */
    String getName();
    
    /**
     * 获取工具描述
     */
    String getDescription();
    
    /**
     * 获取工具参数 Schema
     */
    ToolSchema getSchema();
    
    /**
     * 执行工具
     * @param parameters 工具参数
     * @return 执行结果
     */
    ToolResult execute(Map<String, Object> parameters);
    
    /**
     * 工具 Schema 定义
     */
    class ToolSchema {
        @JsonProperty("type")
        private String type = "object";
        
        @JsonProperty("properties")
        private Map<String, PropertySchema> properties;
        
        @JsonProperty("required")
        private String[] required;
        
        public ToolSchema() {}
        
        public ToolSchema(Map<String, PropertySchema> properties, String[] required) {
            this.properties = properties;
            this.required = required;
        }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public Map<String, PropertySchema> getProperties() { return properties; }
        public void setProperties(Map<String, PropertySchema> properties) { this.properties = properties; }
        
        public String[] getRequired() { return required; }
        public void setRequired(String[] required) { this.required = required; }
    }
    
    /**
     * 属性 Schema 定义
     */
    class PropertySchema {
        @JsonProperty("type")
        private String type;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("enum")
        private String[] enumValues;
        
        public PropertySchema() {}
        
        public PropertySchema(String type, String description) {
            this.type = type;
            this.description = description;
        }
        
        public PropertySchema(String type, String description, String[] enumValues) {
            this.type = type;
            this.description = description;
            this.enumValues = enumValues;
        }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String[] getEnumValues() { return enumValues; }
        public void setEnumValues(String[] enumValues) { this.enumValues = enumValues; }
    }
    
    /**
     * 工具执行结果
     */
    class ToolResult {
        private final boolean success;
        private final String content;
        private final String error;
        
        public ToolResult(boolean success, String content, String error) {
            this.success = success;
            this.content = content;
            this.error = error;
        }
        
        public static ToolResult success(String content) {
            return new ToolResult(true, content, null);
        }
        
        public static ToolResult error(String error) {
            return new ToolResult(false, null, error);
        }
        
        public boolean isSuccess() { return success; }
        public String getContent() { return content; }
        public String getError() { return error; }
        
        @Override
        public String toString() {
            if (success) {
                return content;
            } else {
                return "Error: " + error;
            }
        }
    }
}
