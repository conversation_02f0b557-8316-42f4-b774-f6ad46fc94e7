package com.phodal.migrator.service.code;

import com.phodal.migrator.core.MigrationContext;

import java.util.List;
import java.util.Map;

/**
 * 代码迁移服务接口
 * 负责处理Java代码的转换和升级
 */
public interface CodeMigrationService {
    
    /**
     * 迁移Java代码
     * @param context 迁移上下文
     * @return 迁移结果
     */
    CodeMigrationResult migrateCode(MigrationContext context);
    
    /**
     * 执行javax到jakarta的包名转换
     * @param context 迁移上下文
     * @return 转换的文件数量
     */
    int migrateJavaxToJakarta(MigrationContext context);
    
    /**
     * 升级过时的API调用
     * @param context 迁移上下文
     * @return 升级的API数量
     */
    int upgradeDeprecatedAPIs(MigrationContext context);
    
    /**
     * 应用JDK 21新特性
     * @param context 迁移上下文
     * @return 应用的优化数量
     */
    int applyJDK21Features(MigrationContext context);
    
    /**
     * 代码迁移结果
     */
    class CodeMigrationResult {
        private final boolean success;
        private final String message;
        private final int modifiedFiles;
        private final Map<String, Integer> transformationCounts;
        private final List<String> errors;
        
        public CodeMigrationResult(boolean success, String message, int modifiedFiles,
                                 Map<String, Integer> transformationCounts, List<String> errors) {
            this.success = success;
            this.message = message;
            this.modifiedFiles = modifiedFiles;
            this.transformationCounts = transformationCounts;
            this.errors = errors;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public int getModifiedFiles() { return modifiedFiles; }
        public Map<String, Integer> getTransformationCounts() { return transformationCounts; }
        public List<String> getErrors() { return errors; }
    }
}
