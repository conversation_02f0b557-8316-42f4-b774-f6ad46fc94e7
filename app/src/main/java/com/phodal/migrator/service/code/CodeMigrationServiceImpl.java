package com.phodal.migrator.service.code;

import com.phodal.migrator.core.MigrationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Stream;

/**
 * 代码迁移服务实现
 */
public class CodeMigrationServiceImpl implements CodeMigrationService {
    private static final Logger logger = LoggerFactory.getLogger(CodeMigrationServiceImpl.class);
    
    // javax -> jakarta 包名映射
    private static final Map<String, String> JAVAX_TO_JAKARTA_MAPPINGS = Map.of(
        "javax.servlet", "jakarta.servlet",
        "javax.persistence", "jakarta.persistence",
        "javax.validation", "jakarta.validation",
        "javax.annotation", "jakarta.annotation",
        "javax.transaction", "jakarta.transaction",
        "javax.ejb", "jakarta.ejb",
        "javax.jms", "jakarta.jms",
        "javax.mail", "jakarta.mail"
    );
    
    // 过时API映射
    private static final Map<String, String> DEPRECATED_API_MAPPINGS = Map.of(
        "WebSecurityConfigurerAdapter", "SecurityFilterChain",
        "authorizeRequests()", "authorizeHttpRequests()",
        "antMatchers()", "requestMatchers()",
        "hasRole()", "hasAuthority()"
    );
    
    @Override
    public CodeMigrationResult migrateCode(MigrationContext context) {
        try {
            logger.info("开始代码迁移...");
            
            Map<String, Integer> transformationCounts = new HashMap<>();
            List<String> errors = new ArrayList<>();
            int modifiedFiles = 0;
            
            // 1. javax -> jakarta 转换
            int javaxToJakartaCount = migrateJavaxToJakarta(context);
            transformationCounts.put("javax_to_jakarta", javaxToJakartaCount);
            
            // 2. 升级过时API
            int deprecatedApiCount = upgradeDeprecatedAPIs(context);
            transformationCounts.put("deprecated_apis", deprecatedApiCount);
            
            // 3. 应用JDK 21新特性
            int jdk21FeaturesCount = applyJDK21Features(context);
            transformationCounts.put("jdk21_features", jdk21FeaturesCount);
            
            modifiedFiles = javaxToJakartaCount + deprecatedApiCount + jdk21FeaturesCount;
            
            logger.info("代码迁移完成，修改了 {} 个文件", modifiedFiles);
            return new CodeMigrationResult(true, "代码迁移成功", modifiedFiles, transformationCounts, errors);
            
        } catch (Exception e) {
            logger.error("代码迁移失败: {}", e.getMessage(), e);
            return new CodeMigrationResult(false, "代码迁移失败: " + e.getMessage(), 
                0, Collections.emptyMap(), List.of(e.getMessage()));
        }
    }
    
    @Override
    public int migrateJavaxToJakarta(MigrationContext context) {
        int modifiedFiles = 0;
        
        try {
            Path projectPath = context.getProjectPath();
            
            // 查找所有Java文件
            try (Stream<Path> paths = Files.walk(projectPath)) {
                List<Path> javaFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".java"))
                    .filter(path -> !path.toString().contains("target/")) // 排除构建目录
                    .filter(path -> !path.toString().contains("build/"))  // 排除构建目录
                    .toList();
                
                for (Path javaFile : javaFiles) {
                    if (migrateJavaxToJakartaInFile(javaFile, context)) {
                        modifiedFiles++;
                    }
                }
            }
            
            logger.info("javax -> jakarta 转换完成，修改了 {} 个文件", modifiedFiles);
            
        } catch (Exception e) {
            logger.error("javax -> jakarta 转换失败: {}", e.getMessage(), e);
        }
        
        return modifiedFiles;
    }
    
    @Override
    public int upgradeDeprecatedAPIs(MigrationContext context) {
        int modifiedFiles = 0;
        
        try {
            Path projectPath = context.getProjectPath();
            
            // 查找所有Java文件
            try (Stream<Path> paths = Files.walk(projectPath)) {
                List<Path> javaFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".java"))
                    .filter(path -> !path.toString().contains("target/"))
                    .filter(path -> !path.toString().contains("build/"))
                    .toList();
                
                for (Path javaFile : javaFiles) {
                    if (upgradeDeprecatedAPIsInFile(javaFile, context)) {
                        modifiedFiles++;
                    }
                }
            }
            
            logger.info("过时API升级完成，修改了 {} 个文件", modifiedFiles);
            
        } catch (Exception e) {
            logger.error("过时API升级失败: {}", e.getMessage(), e);
        }
        
        return modifiedFiles;
    }
    
    @Override
    public int applyJDK21Features(MigrationContext context) {
        int modifiedFiles = 0;
        
        try {
            // 检查配置是否启用JDK 21特性应用
            if (context.getOptions().getConfig() != null) {
                // TODO: 从配置中检查是否启用JDK 21特性
            }
            
            Path projectPath = context.getProjectPath();
            
            // 查找所有Java文件
            try (Stream<Path> paths = Files.walk(projectPath)) {
                List<Path> javaFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".java"))
                    .filter(path -> !path.toString().contains("target/"))
                    .filter(path -> !path.toString().contains("build/"))
                    .toList();
                
                for (Path javaFile : javaFiles) {
                    if (applyJDK21FeaturesInFile(javaFile, context)) {
                        modifiedFiles++;
                    }
                }
            }
            
            logger.info("JDK 21特性应用完成，修改了 {} 个文件", modifiedFiles);
            
        } catch (Exception e) {
            logger.error("JDK 21特性应用失败: {}", e.getMessage(), e);
        }
        
        return modifiedFiles;
    }
    
    private boolean migrateJavaxToJakartaInFile(Path javaFile, MigrationContext context) {
        try {
            String content = Files.readString(javaFile);
            String originalContent = content;
            
            // 替换import语句
            for (Map.Entry<String, String> mapping : JAVAX_TO_JAKARTA_MAPPINGS.entrySet()) {
                String javaxPackage = mapping.getKey();
                String jakartaPackage = mapping.getValue();
                
                // 替换import语句
                content = content.replaceAll(
                    "import\\s+" + javaxPackage.replace(".", "\\.") + "\\.",
                    "import " + jakartaPackage + "."
                );
                
                // 替换注解和类引用（简单情况）
                content = content.replaceAll(
                    "\\b" + javaxPackage.replace(".", "\\.") + "\\.",
                    jakartaPackage + "."
                );
            }
            
            // 如果内容有变化，写回文件
            if (!content.equals(originalContent)) {
                if (!context.isDryRun()) {
                    Files.writeString(javaFile, content);
                }
                logger.debug("转换文件: {}", javaFile);
                return true;
            }
            
        } catch (IOException e) {
            logger.error("处理文件失败: {}", javaFile, e);
        }
        
        return false;
    }
    
    private boolean upgradeDeprecatedAPIsInFile(Path javaFile, MigrationContext context) {
        try {
            String content = Files.readString(javaFile);
            String originalContent = content;
            
            // 替换过时的API调用
            for (Map.Entry<String, String> mapping : DEPRECATED_API_MAPPINGS.entrySet()) {
                String oldApi = mapping.getKey();
                String newApi = mapping.getValue();
                
                content = content.replace(oldApi, newApi);
            }
            
            // 如果内容有变化，写回文件
            if (!content.equals(originalContent)) {
                if (!context.isDryRun()) {
                    Files.writeString(javaFile, content);
                }
                logger.debug("升级API: {}", javaFile);
                return true;
            }
            
        } catch (IOException e) {
            logger.error("处理文件失败: {}", javaFile, e);
        }
        
        return false;
    }
    
    private boolean applyJDK21FeaturesInFile(Path javaFile, MigrationContext context) {
        try {
            String content = Files.readString(javaFile);
            String originalContent = content;
            boolean modified = false;
            
            // 应用文本块（Text Blocks）
            content = applyTextBlocks(content);
            
            // 应用模式匹配（Pattern Matching）
            content = applyPatternMatching(content);
            
            // 应用记录类（Records）建议
            content = suggestRecords(content);
            
            modified = !content.equals(originalContent);
            
            // 如果内容有变化，写回文件
            if (modified) {
                if (!context.isDryRun()) {
                    Files.writeString(javaFile, content);
                }
                logger.debug("应用JDK 21特性: {}", javaFile);
                return true;
            }
            
        } catch (IOException e) {
            logger.error("处理文件失败: {}", javaFile, e);
        }
        
        return false;
    }
    
    private String applyTextBlocks(String content) {
        // TODO: 实现文本块转换逻辑
        // 将多行字符串转换为文本块
        return content;
    }
    
    private String applyPatternMatching(String content) {
        // TODO: 实现模式匹配转换逻辑
        // 将instanceof + cast转换为模式匹配
        return content;
    }
    
    private String suggestRecords(String content) {
        // TODO: 实现记录类建议逻辑
        // 识别可以转换为记录类的简单数据类
        return content;
    }
}
