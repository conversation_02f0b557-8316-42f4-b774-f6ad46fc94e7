package com.phodal.migrator.service.ai.tool.impl;

import com.phodal.migrator.service.ai.tool.Tool;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件分析工具
 * 分析文件内容、类型、大小等信息
 */
public class FileAnalysisTool implements Tool {
    
    @Override
    public String getName() {
        return "file_analysis";
    }
    
    @Override
    public String getDescription() {
        return "分析文件内容、类型、大小等信息";
    }
    
    @Override
    public ToolSchema getSchema() {
        Map<String, PropertySchema> properties = new HashMap<>();
        properties.put("file_path", new PropertySchema("string", "要分析的文件路径"));
        properties.put("analysis_type", new PropertySchema("string", "分析类型", 
            new String[]{"basic", "content", "structure", "dependencies"}));
        
        return new ToolSchema(properties, new String[]{"file_path"});
    }
    
    @Override
    public ToolResult execute(Map<String, Object> parameters) {
        try {
            String filePath = (String) parameters.get("file_path");
            String analysisType = (String) parameters.getOrDefault("analysis_type", "basic");
            
            if (filePath == null || filePath.trim().isEmpty()) {
                return ToolResult.error("文件路径不能为空");
            }
            
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                return ToolResult.error("文件不存在: " + filePath);
            }
            
            StringBuilder result = new StringBuilder();
            result.append("文件分析结果:\n");
            result.append("路径: ").append(filePath).append("\n");
            
            switch (analysisType.toLowerCase()) {
                case "basic":
                    return ToolResult.success(analyzeBasic(path, result));
                case "content":
                    return ToolResult.success(analyzeContent(path, result));
                case "structure":
                    return ToolResult.success(analyzeStructure(path, result));
                case "dependencies":
                    return ToolResult.success(analyzeDependencies(path, result));
                default:
                    return ToolResult.error("不支持的分析类型: " + analysisType);
            }
            
        } catch (Exception e) {
            return ToolResult.error("文件分析失败: " + e.getMessage());
        }
    }
    
    private String analyzeBasic(Path path, StringBuilder result) throws IOException {
        result.append("类型: ").append(Files.isDirectory(path) ? "目录" : "文件").append("\n");
        
        if (Files.isRegularFile(path)) {
            result.append("大小: ").append(Files.size(path)).append(" 字节\n");
            result.append("可读: ").append(Files.isReadable(path)).append("\n");
            result.append("可写: ").append(Files.isWritable(path)).append("\n");
            
            String fileName = path.getFileName().toString();
            String extension = "";
            int lastDot = fileName.lastIndexOf('.');
            if (lastDot > 0) {
                extension = fileName.substring(lastDot + 1);
                result.append("扩展名: ").append(extension).append("\n");
            }
        }
        
        return result.toString();
    }
    
    private String analyzeContent(Path path, StringBuilder result) throws IOException {
        if (!Files.isRegularFile(path)) {
            return result.append("只能分析文件内容，不能分析目录").toString();
        }
        
        long size = Files.size(path);
        result.append("文件大小: ").append(size).append(" 字节\n");
        
        if (size > 1024 * 1024) { // 1MB
            result.append("文件过大，跳过内容分析\n");
            return result.toString();
        }
        
        String content = Files.readString(path);
        String[] lines = content.split("\n");
        result.append("行数: ").append(lines.length).append("\n");
        result.append("字符数: ").append(content.length()).append("\n");
        
        // 检测文件类型
        String fileName = path.getFileName().toString().toLowerCase();
        if (fileName.endsWith(".java")) {
            result.append("文件类型: Java 源代码\n");
            analyzeJavaContent(content, result);
        } else if (fileName.endsWith(".xml")) {
            result.append("文件类型: XML 配置文件\n");
        } else if (fileName.endsWith(".properties")) {
            result.append("文件类型: Properties 配置文件\n");
        } else if (fileName.endsWith(".yml") || fileName.endsWith(".yaml")) {
            result.append("文件类型: YAML 配置文件\n");
        }
        
        return result.toString();
    }
    
    private void analyzeJavaContent(String content, StringBuilder result) {
        // 简单的 Java 代码分析
        int classCount = countOccurrences(content, "class ");
        int interfaceCount = countOccurrences(content, "interface ");
        int methodCount = countOccurrences(content, "public ") + countOccurrences(content, "private ") + countOccurrences(content, "protected ");
        
        result.append("类数量: ").append(classCount).append("\n");
        result.append("接口数量: ").append(interfaceCount).append("\n");
        result.append("方法数量(估算): ").append(methodCount).append("\n");
        
        // 检查常见的 Spring Boot 注解
        if (content.contains("@SpringBootApplication")) {
            result.append("包含 Spring Boot 主类\n");
        }
        if (content.contains("@RestController") || content.contains("@Controller")) {
            result.append("包含 Spring MVC 控制器\n");
        }
        if (content.contains("@Service")) {
            result.append("包含 Spring 服务类\n");
        }
        if (content.contains("@Repository")) {
            result.append("包含 Spring 数据访问类\n");
        }
    }
    
    private String analyzeStructure(Path path, StringBuilder result) throws IOException {
        if (Files.isDirectory(path)) {
            result.append("目录结构分析:\n");
            Files.walk(path, 2)
                .forEach(p -> {
                    int depth = p.getNameCount() - path.getNameCount();
                    String indent = "  ".repeat(depth);
                    result.append(indent).append(p.getFileName()).append("\n");
                });
        } else {
            result.append("这是一个文件，无法分析目录结构\n");
        }
        
        return result.toString();
    }
    
    private String analyzeDependencies(Path path, StringBuilder result) throws IOException {
        if (!Files.isRegularFile(path)) {
            return result.append("只能分析文件的依赖关系").toString();
        }
        
        String fileName = path.getFileName().toString().toLowerCase();
        String content = Files.readString(path);
        
        if (fileName.equals("pom.xml")) {
            result.append("Maven 依赖分析:\n");
            analyzeMavenDependencies(content, result);
        } else if (fileName.equals("build.gradle") || fileName.equals("build.gradle.kts")) {
            result.append("Gradle 依赖分析:\n");
            analyzeGradleDependencies(content, result);
        } else if (fileName.endsWith(".java")) {
            result.append("Java 导入分析:\n");
            analyzeJavaImports(content, result);
        } else {
            result.append("不支持此文件类型的依赖分析\n");
        }
        
        return result.toString();
    }
    
    private void analyzeMavenDependencies(String content, StringBuilder result) {
        // 简单的 Maven 依赖分析
        int dependencyCount = countOccurrences(content, "<dependency>");
        result.append("依赖数量: ").append(dependencyCount).append("\n");
        
        if (content.contains("spring-boot-starter")) {
            result.append("包含 Spring Boot Starter 依赖\n");
        }
        if (content.contains("junit")) {
            result.append("包含 JUnit 测试依赖\n");
        }
    }
    
    private void analyzeGradleDependencies(String content, StringBuilder result) {
        // 简单的 Gradle 依赖分析
        int implementationCount = countOccurrences(content, "implementation");
        int testImplementationCount = countOccurrences(content, "testImplementation");
        
        result.append("implementation 依赖数量: ").append(implementationCount).append("\n");
        result.append("testImplementation 依赖数量: ").append(testImplementationCount).append("\n");
    }
    
    private void analyzeJavaImports(String content, StringBuilder result) {
        String[] lines = content.split("\n");
        int importCount = 0;
        
        for (String line : lines) {
            line = line.trim();
            if (line.startsWith("import ") && !line.startsWith("import static")) {
                importCount++;
            }
        }
        
        result.append("导入语句数量: ").append(importCount).append("\n");
        
        if (content.contains("import org.springframework")) {
            result.append("使用 Spring Framework\n");
        }
        if (content.contains("import javax.") || content.contains("import jakarta.")) {
            result.append("使用 Java EE/Jakarta EE\n");
        }
    }
    
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
