package com.phodal.migrator.service.dependency;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.ProjectType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

/**
 * 依赖迁移服务实现
 */
public class DependencyMigrationServiceImpl implements DependencyMigrationService {
    private static final Logger logger = LoggerFactory.getLogger(DependencyMigrationServiceImpl.class);
    
    // Spring Boot 2.x -> 3.x 依赖映射
    private static final Map<String, String> DEPENDENCY_UPGRADES = Map.of(
        "org.springframework.boot:spring-boot-starter-web", "3.2.0",
        "org.springframework.boot:spring-boot-starter-data-jpa", "3.2.0",
        "org.springframework.boot:spring-boot-starter-security", "3.2.0",
        "org.springframework.boot:spring-boot-starter-test", "3.2.0"
    );
    
    // 需要添加的Jakarta EE依赖
    private static final List<String> JAKARTA_DEPENDENCIES = List.of(
        "jakarta.servlet:jakarta.servlet-api:6.0.0",
        "jakarta.persistence:jakarta.persistence-api:3.1.0",
        "jakarta.validation:jakarta.validation-api:3.0.2"
    );
    
    // 需要移除的不兼容依赖
    private static final List<String> INCOMPATIBLE_DEPENDENCIES = List.of(
        "javax.servlet:servlet-api",
        "javax.servlet:javax.servlet-api",
        "org.hibernate.javax.persistence:hibernate-jpa-2.1-api"
    );
    
    @Override
    public DependencyMigrationResult migrateDependencies(MigrationContext context) {
        try {
            logger.info("开始依赖迁移...");
            
            Map<String, String> upgradedDependencies = new HashMap<>();
            List<String> addedDependencies = new ArrayList<>();
            List<String> removedDependencies = new ArrayList<>();
            
            ProjectType projectType = context.getProjectType();
            
            switch (projectType) {
                case MAVEN:
                    migrateMavenDependencies(context, upgradedDependencies, addedDependencies, removedDependencies);
                    break;
                case GRADLE:
                case GRADLE_KTS:
                    migrateGradleDependencies(context, upgradedDependencies, addedDependencies, removedDependencies);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的项目类型: " + projectType);
            }
            
            logger.info("依赖迁移完成");
            return new DependencyMigrationResult(true, "依赖迁移成功", 
                upgradedDependencies, addedDependencies, removedDependencies);
            
        } catch (Exception e) {
            logger.error("依赖迁移失败: {}", e.getMessage(), e);
            return new DependencyMigrationResult(false, "依赖迁移失败: " + e.getMessage(), 
                Collections.emptyMap(), Collections.emptyList(), Collections.emptyList());
        }
    }
    
    @Override
    public boolean upgradeSpringBootVersion(MigrationContext context, String targetVersion) {
        try {
            ProjectType projectType = context.getProjectType();
            Path projectPath = context.getProjectPath();
            
            switch (projectType) {
                case MAVEN:
                    return upgradeSpringBootInPom(projectPath, targetVersion);
                case GRADLE:
                    return upgradeSpringBootInGradle(projectPath, targetVersion);
                case GRADLE_KTS:
                    return upgradeSpringBootInGradleKts(projectPath, targetVersion);
                default:
                    logger.warn("不支持的项目类型: {}", projectType);
                    return false;
            }
        } catch (Exception e) {
            logger.error("升级Spring Boot版本失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public boolean addJakartaEEDependencies(MigrationContext context) {
        try {
            ProjectType projectType = context.getProjectType();
            Path projectPath = context.getProjectPath();
            
            switch (projectType) {
                case MAVEN:
                    return addJakartaDependenciesToPom(projectPath);
                case GRADLE:
                case GRADLE_KTS:
                    return addJakartaDependenciesToGradle(projectPath);
                default:
                    logger.warn("不支持的项目类型: {}", projectType);
                    return false;
            }
        } catch (Exception e) {
            logger.error("添加Jakarta EE依赖失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    @Override
    public List<String> removeIncompatibleDependencies(MigrationContext context) {
        List<String> removedDependencies = new ArrayList<>();
        
        try {
            ProjectType projectType = context.getProjectType();
            Path projectPath = context.getProjectPath();
            
            switch (projectType) {
                case MAVEN:
                    removedDependencies = removeIncompatibleFromPom(projectPath);
                    break;
                case GRADLE:
                case GRADLE_KTS:
                    removedDependencies = removeIncompatibleFromGradle(projectPath);
                    break;
                default:
                    logger.warn("不支持的项目类型: {}", projectType);
            }
        } catch (Exception e) {
            logger.error("移除不兼容依赖失败: {}", e.getMessage(), e);
        }
        
        return removedDependencies;
    }
    
    private void migrateMavenDependencies(MigrationContext context, 
                                        Map<String, String> upgradedDependencies,
                                        List<String> addedDependencies, 
                                        List<String> removedDependencies) {
        // 升级Spring Boot版本
        if (upgradeSpringBootVersion(context, context.getTargetSpringBootVersion())) {
            upgradedDependencies.put("org.springframework.boot", context.getTargetSpringBootVersion());
        }
        
        // 添加Jakarta EE依赖
        if (addJakartaEEDependencies(context)) {
            addedDependencies.addAll(JAKARTA_DEPENDENCIES);
        }
        
        // 移除不兼容依赖
        removedDependencies.addAll(removeIncompatibleDependencies(context));
    }
    
    private void migrateGradleDependencies(MigrationContext context,
                                         Map<String, String> upgradedDependencies,
                                         List<String> addedDependencies,
                                         List<String> removedDependencies) {
        // 类似Maven的处理逻辑
        migrateMavenDependencies(context, upgradedDependencies, addedDependencies, removedDependencies);
    }
    
    private boolean upgradeSpringBootInPom(Path projectPath, String targetVersion) throws IOException {
        Path pomFile = projectPath.resolve("pom.xml");
        if (!Files.exists(pomFile)) {
            return false;
        }
        
        // TODO: 实现pom.xml中Spring Boot版本升级
        logger.info("升级pom.xml中的Spring Boot版本到: {}", targetVersion);
        return true;
    }
    
    private boolean upgradeSpringBootInGradle(Path projectPath, String targetVersion) throws IOException {
        Path gradleFile = projectPath.resolve("build.gradle");
        if (!Files.exists(gradleFile)) {
            return false;
        }
        
        // TODO: 实现build.gradle中Spring Boot版本升级
        logger.info("升级build.gradle中的Spring Boot版本到: {}", targetVersion);
        return true;
    }
    
    private boolean upgradeSpringBootInGradleKts(Path projectPath, String targetVersion) throws IOException {
        Path gradleKtsFile = projectPath.resolve("build.gradle.kts");
        if (!Files.exists(gradleKtsFile)) {
            return false;
        }
        
        // TODO: 实现build.gradle.kts中Spring Boot版本升级
        logger.info("升级build.gradle.kts中的Spring Boot版本到: {}", targetVersion);
        return true;
    }
    
    private boolean addJakartaDependenciesToPom(Path projectPath) throws IOException {
        // TODO: 实现向pom.xml添加Jakarta EE依赖
        logger.info("向pom.xml添加Jakarta EE依赖");
        return true;
    }
    
    private boolean addJakartaDependenciesToGradle(Path projectPath) throws IOException {
        // TODO: 实现向Gradle文件添加Jakarta EE依赖
        logger.info("向Gradle文件添加Jakarta EE依赖");
        return true;
    }
    
    private List<String> removeIncompatibleFromPom(Path projectPath) throws IOException {
        // TODO: 实现从pom.xml移除不兼容依赖
        logger.info("从pom.xml移除不兼容依赖");
        return new ArrayList<>(INCOMPATIBLE_DEPENDENCIES);
    }
    
    private List<String> removeIncompatibleFromGradle(Path projectPath) throws IOException {
        // TODO: 实现从Gradle文件移除不兼容依赖
        logger.info("从Gradle文件移除不兼容依赖");
        return new ArrayList<>(INCOMPATIBLE_DEPENDENCIES);
    }
}
