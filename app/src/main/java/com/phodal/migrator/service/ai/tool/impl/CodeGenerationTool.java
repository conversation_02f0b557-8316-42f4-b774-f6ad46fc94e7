package com.phodal.migrator.service.ai.tool.impl;

import com.phodal.migrator.service.ai.tool.Tool;
import java.util.HashMap;
import java.util.Map;

/**
 * 代码生成工具
 * 生成常见的代码模板和结构
 */
public class CodeGenerationTool implements Tool {
    
    @Override
    public String getName() {
        return "code_generation";
    }
    
    @Override
    public String getDescription() {
        return "生成常见的代码模板和结构，如 Spring Boot 类、配置文件等";
    }
    
    @Override
    public ToolSchema getSchema() {
        Map<String, PropertySchema> properties = new HashMap<>();
        properties.put("template_type", new PropertySchema("string", "模板类型", 
            new String[]{"controller", "service", "repository", "entity", "config", "test"}));
        properties.put("class_name", new PropertySchema("string", "类名"));
        properties.put("package_name", new PropertySchema("string", "包名"));
        properties.put("additional_options", new PropertySchema("object", "额外选项"));
        
        return new ToolSchema(properties, new String[]{"template_type", "class_name"});
    }
    
    @Override
    public ToolResult execute(Map<String, Object> parameters) {
        try {
            String templateType = (String) parameters.get("template_type");
            String className = (String) parameters.get("class_name");
            String packageName = (String) parameters.getOrDefault("package_name", "com.example");
            
            if (templateType == null || className == null) {
                return ToolResult.error("模板类型和类名不能为空");
            }
            
            String code = generateCode(templateType, className, packageName, parameters);
            return ToolResult.success(code);
            
        } catch (Exception e) {
            return ToolResult.error("代码生成失败: " + e.getMessage());
        }
    }
    
    private String generateCode(String templateType, String className, String packageName, Map<String, Object> parameters) {
        switch (templateType.toLowerCase()) {
            case "controller":
                return generateController(className, packageName);
            case "service":
                return generateService(className, packageName);
            case "repository":
                return generateRepository(className, packageName);
            case "entity":
                return generateEntity(className, packageName);
            case "config":
                return generateConfig(className, packageName);
            case "test":
                return generateTest(className, packageName);
            default:
                throw new IllegalArgumentException("不支持的模板类型: " + templateType);
        }
    }
    
    private String generateController(String className, String packageName) {
        return String.format("""
            package %s.controller;
            
            import org.springframework.beans.factory.annotation.Autowired;
            import org.springframework.http.ResponseEntity;
            import org.springframework.web.bind.annotation.*;
            import %s.service.%sService;
            
            /**
             * %s 控制器
             */
            @RestController
            @RequestMapping("/api/%s")
            public class %sController {
                
                @Autowired
                private %sService %sService;
                
                @GetMapping
                public ResponseEntity<?> getAll() {
                    // TODO: 实现获取所有记录的逻辑
                    return ResponseEntity.ok().build();
                }
                
                @GetMapping("/{id}")
                public ResponseEntity<?> getById(@PathVariable Long id) {
                    // TODO: 实现根据ID获取记录的逻辑
                    return ResponseEntity.ok().build();
                }
                
                @PostMapping
                public ResponseEntity<?> create(@RequestBody Object request) {
                    // TODO: 实现创建记录的逻辑
                    return ResponseEntity.ok().build();
                }
                
                @PutMapping("/{id}")
                public ResponseEntity<?> update(@PathVariable Long id, @RequestBody Object request) {
                    // TODO: 实现更新记录的逻辑
                    return ResponseEntity.ok().build();
                }
                
                @DeleteMapping("/{id}")
                public ResponseEntity<?> delete(@PathVariable Long id) {
                    // TODO: 实现删除记录的逻辑
                    return ResponseEntity.ok().build();
                }
            }
            """, packageName, packageName, className, className, 
            className.toLowerCase(), className, className, 
            className.substring(0, 1).toLowerCase() + className.substring(1));
    }
    
    private String generateService(String className, String packageName) {
        return String.format("""
            package %s.service;
            
            import org.springframework.stereotype.Service;
            
            /**
             * %s 服务类
             */
            @Service
            public class %sService {
                
                /**
                 * 获取所有记录
                 */
                public Object findAll() {
                    // TODO: 实现业务逻辑
                    return null;
                }
                
                /**
                 * 根据ID获取记录
                 */
                public Object findById(Long id) {
                    // TODO: 实现业务逻辑
                    return null;
                }
                
                /**
                 * 创建记录
                 */
                public Object create(Object entity) {
                    // TODO: 实现业务逻辑
                    return null;
                }
                
                /**
                 * 更新记录
                 */
                public Object update(Long id, Object entity) {
                    // TODO: 实现业务逻辑
                    return null;
                }
                
                /**
                 * 删除记录
                 */
                public void delete(Long id) {
                    // TODO: 实现业务逻辑
                }
            }
            """, packageName, className, className);
    }
    
    private String generateRepository(String className, String packageName) {
        return String.format("""
            package %s.repository;
            
            import org.springframework.data.jpa.repository.JpaRepository;
            import org.springframework.stereotype.Repository;
            import %s.entity.%s;
            
            /**
             * %s 数据访问接口
             */
            @Repository
            public interface %sRepository extends JpaRepository<%s, Long> {
                
                // 可以在这里添加自定义查询方法
                // 例如：
                // List<%s> findByName(String name);
                // Optional<%s> findByEmail(String email);
            }
            """, packageName, packageName, className, className, className, className, className, className);
    }
    
    private String generateEntity(String className, String packageName) {
        return String.format("""
            package %s.entity;
            
            import jakarta.persistence.*;
            import java.time.LocalDateTime;
            
            /**
             * %s 实体类
             */
            @Entity
            @Table(name = "%s")
            public class %s {
                
                @Id
                @GeneratedValue(strategy = GenerationType.IDENTITY)
                private Long id;
                
                @Column(name = "created_at")
                private LocalDateTime createdAt;
                
                @Column(name = "updated_at")
                private LocalDateTime updatedAt;
                
                // 构造函数
                public %s() {
                    this.createdAt = LocalDateTime.now();
                    this.updatedAt = LocalDateTime.now();
                }
                
                // Getter 和 Setter 方法
                public Long getId() {
                    return id;
                }
                
                public void setId(Long id) {
                    this.id = id;
                }
                
                public LocalDateTime getCreatedAt() {
                    return createdAt;
                }
                
                public void setCreatedAt(LocalDateTime createdAt) {
                    this.createdAt = createdAt;
                }
                
                public LocalDateTime getUpdatedAt() {
                    return updatedAt;
                }
                
                public void setUpdatedAt(LocalDateTime updatedAt) {
                    this.updatedAt = updatedAt;
                }
                
                @PreUpdate
                public void preUpdate() {
                    this.updatedAt = LocalDateTime.now();
                }
            }
            """, packageName, className, className.toLowerCase(), className, className);
    }
    
    private String generateConfig(String className, String packageName) {
        return String.format("""
            package %s.config;
            
            import org.springframework.context.annotation.Configuration;
            
            /**
             * %s 配置类
             */
            @Configuration
            public class %sConfig {
                
                // 在这里添加配置相关的 Bean 定义
                // 例如：
                // @Bean
                // public SomeService someService() {
                //     return new SomeService();
                // }
            }
            """, packageName, className, className);
    }
    
    private String generateTest(String className, String packageName) {
        return String.format("""
            package %s;
            
            import org.junit.jupiter.api.Test;
            import org.junit.jupiter.api.BeforeEach;
            import org.junit.jupiter.api.DisplayName;
            import org.springframework.boot.test.context.SpringBootTest;
            import static org.junit.jupiter.api.Assertions.*;
            
            /**
             * %s 测试类
             */
            @SpringBootTest
            @DisplayName("%s 测试")
            public class %sTest {
                
                @BeforeEach
                void setUp() {
                    // 测试前的准备工作
                }
                
                @Test
                @DisplayName("测试基本功能")
                void testBasicFunctionality() {
                    // TODO: 实现测试逻辑
                    assertTrue(true, "这是一个示例测试");
                }
                
                @Test
                @DisplayName("测试异常情况")
                void testExceptionHandling() {
                    // TODO: 实现异常测试逻辑
                    assertThrows(Exception.class, () -> {
                        // 触发异常的代码
                    });
                }
            }
            """, packageName, className, className, className);
    }
}
