package com.phodal.migrator.service.dependency;

import com.phodal.migrator.core.MigrationContext;

import java.util.List;
import java.util.Map;

/**
 * 依赖迁移服务接口
 * 负责处理Maven/Gradle依赖的升级和迁移
 */
public interface DependencyMigrationService {
    
    /**
     * 迁移项目依赖
     * @param context 迁移上下文
     * @return 迁移结果
     */
    DependencyMigrationResult migrateDependencies(MigrationContext context);
    
    /**
     * 升级Spring Boot版本
     * @param context 迁移上下文
     * @param targetVersion 目标版本
     * @return 升级结果
     */
    boolean upgradeSpringBootVersion(MigrationContext context, String targetVersion);
    
    /**
     * 添加Jakarta EE依赖
     * @param context 迁移上下文
     * @return 添加结果
     */
    boolean addJakartaEEDependencies(MigrationContext context);
    
    /**
     * 移除不兼容的依赖
     * @param context 迁移上下文
     * @return 移除的依赖列表
     */
    List<String> removeIncompatibleDependencies(MigrationContext context);
    
    /**
     * 依赖迁移结果
     */
    class DependencyMigrationResult {
        private final boolean success;
        private final String message;
        private final Map<String, String> upgradedDependencies;
        private final List<String> addedDependencies;
        private final List<String> removedDependencies;
        
        public DependencyMigrationResult(boolean success, String message, 
                                       Map<String, String> upgradedDependencies,
                                       List<String> addedDependencies,
                                       List<String> removedDependencies) {
            this.success = success;
            this.message = message;
            this.upgradedDependencies = upgradedDependencies;
            this.addedDependencies = addedDependencies;
            this.removedDependencies = removedDependencies;
        }
        
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Map<String, String> getUpgradedDependencies() { return upgradedDependencies; }
        public List<String> getAddedDependencies() { return addedDependencies; }
        public List<String> getRemovedDependencies() { return removedDependencies; }
    }
}
