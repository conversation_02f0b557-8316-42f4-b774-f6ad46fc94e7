package com.phodal.migrator.service.ai.tool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具注册表
 * 管理所有可用的工具
 */
public class ToolRegistry {
    
    private final Map<String, Tool> tools = new ConcurrentHashMap<>();
    
    /**
     * 注册工具
     */
    public void registerTool(Tool tool) {
        tools.put(tool.getName(), tool);
    }
    
    /**
     * 获取工具
     */
    public Optional<Tool> getTool(String name) {
        return Optional.ofNullable(tools.get(name));
    }
    
    /**
     * 获取所有工具
     */
    public Collection<Tool> getAllTools() {
        return tools.values();
    }
    
    /**
     * 获取所有工具名称
     */
    public Set<String> getToolNames() {
        return tools.keySet();
    }
    
    /**
     * 执行工具
     */
    public Tool.ToolResult executeTool(String toolName, Map<String, Object> parameters) {
        Optional<Tool> tool = getTool(toolName);
        if (tool.isPresent()) {
            try {
                return tool.get().execute(parameters);
            } catch (Exception e) {
                return Tool.ToolResult.error("工具执行失败: " + e.getMessage());
            }
        } else {
            return Tool.ToolResult.error("未找到工具: " + toolName);
        }
    }
    
    /**
     * 检查工具是否存在
     */
    public boolean hasTool(String name) {
        return tools.containsKey(name);
    }
    
    /**
     * 移除工具
     */
    public void removeTool(String name) {
        tools.remove(name);
    }
    
    /**
     * 清空所有工具
     */
    public void clear() {
        tools.clear();
    }
    
    /**
     * 获取工具数量
     */
    public int size() {
        return tools.size();
    }
}
