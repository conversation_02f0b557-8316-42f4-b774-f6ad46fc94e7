package com.phodal.migrator.service.analysis;

import com.phodal.migrator.core.MigrationContext;

import java.nio.file.Path;
import java.util.Map;

/**
 * 项目分析服务接口
 * 负责分析项目结构、版本信息等
 */
public interface ProjectAnalysisService {
    
    /**
     * 分析项目基本信息
     * @param projectPath 项目路径
     * @return 分析结果
     */
    ProjectAnalysisResult analyzeProject(Path projectPath);
    
    /**
     * 检测Spring Boot版本
     * @param context 迁移上下文
     * @return Spring Boot版本
     */
    String detectSpringBootVersion(MigrationContext context);
    
    /**
     * 检测Java版本
     * @param context 迁移上下文
     * @return Java版本
     */
    String detectJavaVersion(MigrationContext context);
    
    /**
     * 创建项目备份
     * @param context 迁移上下文
     * @return 备份路径
     */
    Path createBackup(MigrationContext context);
    
    /**
     * 项目分析结果
     */
    class ProjectAnalysisResult {
        private final Map<String, Object> analysisData;
        private final boolean success;
        private final String message;
        
        public ProjectAnalysisResult(Map<String, Object> analysisData, boolean success, String message) {
            this.analysisData = analysisData;
            this.success = success;
            this.message = message;
        }
        
        public Map<String, Object> getAnalysisData() { return analysisData; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }
}
