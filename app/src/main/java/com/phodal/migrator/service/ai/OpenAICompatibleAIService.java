package com.phodal.migrator.service.ai;

import com.phodal.migrator.config.AIProviderConfig;
import com.phodal.migrator.service.ai.tool.Tool;
import com.phodal.migrator.service.ai.tool.ToolRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * OpenAI 兼容的 AI 服务实现
 * 支持 DeepSeek、ChatGLM 等兼容 OpenAI API 的服务
 *
 * 注意：这是一个简化的实现，主要用于演示架构
 * 实际生产环境中需要集成真实的 OpenAI SDK
 */
public class OpenAICompatibleAIService implements AIService {

    private static final Logger logger = LoggerFactory.getLogger(OpenAICompatibleAIService.class);

    private final AIProviderConfig.ProviderConfig providerConfig;
    private final ToolRegistry toolRegistry;

    public OpenAICompatibleAIService(AIProviderConfig.ProviderConfig providerConfig, ToolRegistry toolRegistry) {
        this.providerConfig = providerConfig;
        this.toolRegistry = toolRegistry;

        logger.info("初始化 AI 服务: {} ({})",
            providerConfig.getDisplayName(),
            providerConfig.getBaseUrl());
    }

    @Override
    public AIResponse generateMigrationSuggestion(MigrationRequest request) {
        try {
            String prompt = buildMigrationPrompt(request);

            logger.info("生成迁移建议，使用提供商: {}", providerConfig.getDisplayName());
            logger.debug("请求内容: {}", prompt);

            // 模拟 AI 响应（实际实现中应该调用真实的 API）
            String mockResponse = generateMockMigrationResponse(request);

            // 演示工具调用
            String toolResults = executeToolsIfNeeded(request);
            if (toolResults != null) {
                mockResponse += "\n\n=== 工具执行结果 ===\n" + toolResults;
            }

            return new AIResponse(mockResponse, true, null);

        } catch (Exception e) {
            logger.error("生成迁移建议失败", e);
            return new AIResponse(null, false, "生成迁移建议失败: " + e.getMessage());
        }
    }
    
    @Override
    public CodeFixSuggestion suggestCodeFix(CompilationError error) {
        try {
            String prompt = buildCodeFixPrompt(error);
            
            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), 
                    "你是一个专业的 Java 代码修复助手。请分析编译错误并提供修复建议。"),
                new ChatMessage(ChatMessageRole.USER.value(), prompt)
            );
            
            ChatCompletionRequest chatRequest = ChatCompletionRequest.builder()
                    .model(providerConfig.getModel())
                    .messages(messages)
                    .maxTokens(1500)
                    .temperature(0.1)
                    .build();
            
            ChatCompletionResult result = openAiService.createChatCompletion(chatRequest);
            
            String response = result.getChoices().get(0).getMessage().getContent();
            
            // 简单解析响应，实际应用中可能需要更复杂的解析逻辑
            return new CodeFixSuggestion(
                extractFixedCode(response),
                extractExplanation(response),
                extractSteps(response)
            );
            
        } catch (Exception e) {
            return new CodeFixSuggestion(
                null,
                "代码修复建议生成失败: " + e.getMessage(),
                Arrays.asList("请检查网络连接和 API 配置")
            );
        }
    }
    
    @Override
    public DependencyRecommendation recommendDependencyUpgrade(String currentDependency, String targetVersion) {
        try {
            String prompt = String.format(
                "请分析从 %s 升级到 %s 的兼容性问题和建议。请提供：\n" +
                "1. 推荐的版本\n" +
                "2. 可能的兼容性问题\n" +
                "3. 相关依赖的建议版本\n" +
                "4. 需要的配置更改",
                currentDependency, targetVersion
            );
            
            List<ChatMessage> messages = Arrays.asList(
                new ChatMessage(ChatMessageRole.SYSTEM.value(), 
                    "你是一个专业的 Java 依赖管理专家。"),
                new ChatMessage(ChatMessageRole.USER.value(), prompt)
            );
            
            ChatCompletionRequest chatRequest = ChatCompletionRequest.builder()
                    .model(providerConfig.getModel())
                    .messages(messages)
                    .maxTokens(1000)
                    .temperature(0.1)
                    .build();
            
            ChatCompletionResult result = openAiService.createChatCompletion(chatRequest);
            String response = result.getChoices().get(0).getMessage().getContent();
            
            // 简单解析响应
            return new DependencyRecommendation(
                targetVersion,
                Arrays.asList("请查看详细响应"),
                new HashMap<>(),
                Arrays.asList(response)
            );
            
        } catch (Exception e) {
            return new DependencyRecommendation(
                targetVersion,
                Arrays.asList("依赖推荐生成失败: " + e.getMessage()),
                new HashMap<>(),
                Arrays.asList("请检查网络连接和 API 配置")
            );
        }
    }
    
    private String buildMigrationPrompt(MigrationRequest request) {
        return String.format(
            "请分析以下代码的 %s 迁移需求：\n\n" +
            "源代码：\n%s\n\n" +
            "上下文：%s\n\n" +
            "请提供详细的迁移建议，包括需要修改的代码、配置变更和注意事项。",
            request.getMigrationType(),
            request.getSourceCode(),
            request.getContext()
        );
    }
    
    private String buildCodeFixPrompt(CompilationError error) {
        return String.format(
            "请修复以下编译错误：\n\n" +
            "文件：%s (第 %d 行)\n" +
            "错误信息：%s\n\n" +
            "源代码：\n%s\n\n" +
            "请提供修复后的代码和详细说明。",
            error.getFileName(),
            error.getLineNumber(),
            error.getErrorMessage(),
            error.getSourceCode()
        );
    }
    
    private List<ChatTool> buildToolDefinitions() {
        return toolRegistry.getAllTools().stream()
                .map(this::convertToOpenAITool)
                .collect(Collectors.toList());
    }
    
    private ChatTool convertToOpenAITool(Tool tool) {
        try {
            String schemaJson = objectMapper.writeValueAsString(tool.getSchema());
            JsonNode schemaNode = objectMapper.readTree(schemaJson);
            
            ChatFunction function = ChatFunction.builder()
                    .name(tool.getName())
                    .description(tool.getDescription())
                    .parameters(schemaNode)
                    .build();
            
            return new ChatTool(function);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换工具定义失败: " + e.getMessage(), e);
        }
    }
    
    private AIResponse processResponse(ChatCompletionResult result) {
        try {
            ChatMessage message = result.getChoices().get(0).getMessage();
            StringBuilder responseContent = new StringBuilder();
            
            // 处理文本响应
            if (message.getContent() != null) {
                responseContent.append(message.getContent()).append("\n");
            }
            
            // 处理工具调用
            if (message.getToolCalls() != null && !message.getToolCalls().isEmpty()) {
                responseContent.append("\n=== 工具执行结果 ===\n");
                
                for (ChatToolCall toolCall : message.getToolCalls()) {
                    String toolName = toolCall.getFunction().getName();
                    String argumentsJson = toolCall.getFunction().getArguments();
                    
                    try {
                        Map<String, Object> arguments = objectMapper.readValue(argumentsJson, Map.class);
                        Tool.ToolResult toolResult = toolRegistry.executeTool(toolName, arguments);
                        
                        responseContent.append(String.format("工具: %s\n", toolName));
                        responseContent.append(String.format("结果: %s\n\n", toolResult.toString()));
                        
                    } catch (Exception e) {
                        responseContent.append(String.format("工具 %s 执行失败: %s\n\n", toolName, e.getMessage()));
                    }
                }
            }
            
            return new AIResponse(responseContent.toString(), true, null);
            
        } catch (Exception e) {
            return new AIResponse(null, false, "处理响应失败: " + e.getMessage());
        }
    }
    
    private String extractFixedCode(String response) {
        // 简单的代码提取逻辑，实际应用中可能需要更复杂的解析
        int codeStart = response.indexOf("```java");
        if (codeStart != -1) {
            int codeEnd = response.indexOf("```", codeStart + 7);
            if (codeEnd != -1) {
                return response.substring(codeStart + 7, codeEnd).trim();
            }
        }
        return null;
    }
    
    private String extractExplanation(String response) {
        // 提取解释部分
        return response.split("\n")[0]; // 简单实现，取第一行作为解释
    }
    
    private List<String> extractSteps(String response) {
        // 提取步骤列表
        List<String> steps = new ArrayList<>();
        String[] lines = response.split("\n");
        for (String line : lines) {
            if (line.trim().matches("^\\d+\\..*") || line.trim().startsWith("- ")) {
                steps.add(line.trim());
            }
        }
        return steps.isEmpty() ? Arrays.asList("请查看完整响应") : steps;
    }
}
