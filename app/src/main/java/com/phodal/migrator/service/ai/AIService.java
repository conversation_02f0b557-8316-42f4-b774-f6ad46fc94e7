package com.phodal.migrator.service.ai;

import java.util.List;
import java.util.Map;

/**
 * AI服务统一接口
 * 提供AI辅助的迁移建议和代码修复功能
 */
public interface AIService {
    
    /**
     * 生成迁移建议
     * @param request 迁移请求
     * @return AI响应
     */
    AIResponse generateMigrationSuggestion(MigrationRequest request);
    
    /**
     * 建议代码修复方案
     * @param error 编译错误信息
     * @return 修复建议
     */
    CodeFixSuggestion suggestCodeFix(CompilationError error);
    
    /**
     * 推荐依赖升级方案
     * @param currentDependency 当前依赖
     * @param targetVersion 目标版本
     * @return 升级建议
     */
    DependencyRecommendation recommendDependencyUpgrade(String currentDependency, String targetVersion);
    
    /**
     * AI响应
     */
    class AIResponse {
        private final String content;
        private final boolean success;
        private final String errorMessage;
        
        public AIResponse(String content, boolean success, String errorMessage) {
            this.content = content;
            this.success = success;
            this.errorMessage = errorMessage;
        }
        
        public String getContent() { return content; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 迁移请求
     */
    class MigrationRequest {
        private final String sourceCode;
        private final String context;
        private final String migrationType;
        
        public MigrationRequest(String sourceCode, String context, String migrationType) {
            this.sourceCode = sourceCode;
            this.context = context;
            this.migrationType = migrationType;
        }
        
        public String getSourceCode() { return sourceCode; }
        public String getContext() { return context; }
        public String getMigrationType() { return migrationType; }
    }
    
    /**
     * 编译错误信息
     */
    class CompilationError {
        private final String fileName;
        private final int lineNumber;
        private final String errorMessage;
        private final String sourceCode;
        
        public CompilationError(String fileName, int lineNumber, String errorMessage, String sourceCode) {
            this.fileName = fileName;
            this.lineNumber = lineNumber;
            this.errorMessage = errorMessage;
            this.sourceCode = sourceCode;
        }
        
        public String getFileName() { return fileName; }
        public int getLineNumber() { return lineNumber; }
        public String getErrorMessage() { return errorMessage; }
        public String getSourceCode() { return sourceCode; }
    }
    
    /**
     * 代码修复建议
     */
    class CodeFixSuggestion {
        private final String fixedCode;
        private final String explanation;
        private final List<String> steps;
        
        public CodeFixSuggestion(String fixedCode, String explanation, List<String> steps) {
            this.fixedCode = fixedCode;
            this.explanation = explanation;
            this.steps = steps;
        }
        
        public String getFixedCode() { return fixedCode; }
        public String getExplanation() { return explanation; }
        public List<String> getSteps() { return steps; }
    }
    
    /**
     * 依赖推荐
     */
    class DependencyRecommendation {
        private final String recommendedVersion;
        private final List<String> compatibilityIssues;
        private final Map<String, String> relatedDependencies;
        private final List<String> configChanges;
        
        public DependencyRecommendation(String recommendedVersion, List<String> compatibilityIssues,
                                      Map<String, String> relatedDependencies, List<String> configChanges) {
            this.recommendedVersion = recommendedVersion;
            this.compatibilityIssues = compatibilityIssues;
            this.relatedDependencies = relatedDependencies;
            this.configChanges = configChanges;
        }
        
        public String getRecommendedVersion() { return recommendedVersion; }
        public List<String> getCompatibilityIssues() { return compatibilityIssues; }
        public Map<String, String> getRelatedDependencies() { return relatedDependencies; }
        public List<String> getConfigChanges() { return configChanges; }
    }
}
