package com.phodal.migrator.core;

/**
 * 迁移步骤接口
 * 每个迁移步骤都需要实现此接口
 */
public interface MigrationStep {
    
    /**
     * 获取步骤名称
     * @return 步骤名称
     */
    String getName();
    
    /**
     * 获取步骤描述
     * @return 步骤描述
     */
    String getDescription();
    
    /**
     * 是否为必需步骤
     * @return true表示必需，false表示可选
     */
    boolean isRequired();
    
    /**
     * 执行迁移步骤
     * @param context 迁移上下文
     * @return 步骤执行结果
     */
    StepResult execute(MigrationContext context);
    
    /**
     * 验证步骤执行前的条件
     * @param context 迁移上下文
     * @return 验证结果
     */
    default ValidationResult validate(MigrationContext context) {
        return ValidationResult.success();
    }
    
    /**
     * 获取步骤的预估执行时间（秒）
     * @return 预估执行时间
     */
    default int getEstimatedDurationSeconds() {
        return 60; // 默认1分钟
    }
}
