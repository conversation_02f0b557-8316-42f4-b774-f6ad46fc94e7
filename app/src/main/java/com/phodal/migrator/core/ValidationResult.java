package com.phodal.migrator.core;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果
 */
public class ValidationResult {
    private final boolean valid;
    private final List<String> errors;
    private final List<String> warnings;
    
    private ValidationResult(boolean valid, List<String> errors, List<String> warnings) {
        this.valid = valid;
        this.errors = new ArrayList<>(errors);
        this.warnings = new ArrayList<>(warnings);
    }
    
    public boolean isValid() {
        return valid;
    }
    
    public List<String> getErrors() {
        return errors;
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    // 静态工厂方法
    public static ValidationResult success() {
        return new ValidationResult(true, List.of(), List.of());
    }
    
    public static ValidationResult failure(String error) {
        return new ValidationResult(false, List.of(error), List.of());
    }
    
    public static ValidationResult failure(List<String> errors) {
        return new ValidationResult(false, errors, List.of());
    }
    
    public static ValidationResult withWarnings(List<String> warnings) {
        return new ValidationResult(true, List.of(), warnings);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        
        public Builder addError(String error) {
            this.errors.add(error);
            this.valid = false;
            return this;
        }
        
        public Builder addWarning(String warning) {
            this.warnings.add(warning);
            return this;
        }
        
        public Builder valid(boolean valid) {
            this.valid = valid;
            return this;
        }
        
        public ValidationResult build() {
            return new ValidationResult(valid, errors, warnings);
        }
    }
}
