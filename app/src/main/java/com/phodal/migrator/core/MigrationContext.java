package com.phodal.migrator.core;

import java.nio.file.Path;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 迁移上下文
 * 包含迁移过程中的所有状态信息
 */
public class MigrationContext {
    private final Path projectPath;
    private final Path targetPath;
    private final MigrationOptions options;
    private final Map<String, Object> properties;
    private final Instant startTime;
    
    // 项目信息
    private ProjectType projectType;
    private String currentSpringBootVersion;
    private String targetSpringBootVersion;
    private String currentJavaVersion;
    private String targetJavaVersion;
    
    public MigrationContext(Path projectPath, Path targetPath, MigrationOptions options) {
        this.projectPath = projectPath;
        this.targetPath = targetPath;
        this.options = options;
        this.properties = new HashMap<>();
        this.startTime = Instant.now();
    }
    
    public Path getProjectPath() {
        return projectPath;
    }
    
    public Path getTargetPath() {
        return targetPath;
    }
    
    public MigrationOptions getOptions() {
        return options;
    }
    
    public boolean isDryRun() {
        return options.isDryRun();
    }
    
    public boolean isVerbose() {
        return options.isVerbose();
    }
    
    public Instant getStartTime() {
        return startTime;
    }
    
    // 项目类型相关
    public ProjectType getProjectType() {
        return projectType;
    }
    
    public void setProjectType(ProjectType projectType) {
        this.projectType = projectType;
    }
    
    // Spring Boot 版本相关
    public String getCurrentSpringBootVersion() {
        return currentSpringBootVersion;
    }
    
    public void setCurrentSpringBootVersion(String currentSpringBootVersion) {
        this.currentSpringBootVersion = currentSpringBootVersion;
    }
    
    public String getTargetSpringBootVersion() {
        return targetSpringBootVersion;
    }
    
    public void setTargetSpringBootVersion(String targetSpringBootVersion) {
        this.targetSpringBootVersion = targetSpringBootVersion;
    }
    
    // Java 版本相关
    public String getCurrentJavaVersion() {
        return currentJavaVersion;
    }
    
    public void setCurrentJavaVersion(String currentJavaVersion) {
        this.currentJavaVersion = currentJavaVersion;
    }
    
    public String getTargetJavaVersion() {
        return targetJavaVersion;
    }
    
    public void setTargetJavaVersion(String targetJavaVersion) {
        this.targetJavaVersion = targetJavaVersion;
    }
    
    // 属性管理
    public void setProperty(String key, Object value) {
        properties.put(key, value);
    }
    
    @SuppressWarnings("unchecked")
    public <T> T getProperty(String key, Class<T> type) {
        Object value = properties.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }
    
    public Object getProperty(String key) {
        return properties.get(key);
    }
    
    public boolean hasProperty(String key) {
        return properties.containsKey(key);
    }
    
    /**
     * 更新上下文信息基于步骤结果
     */
    public void updateFromStepResult(StepResult result) {
        if (result.getDetails() != null) {
            result.getDetails().forEach(this::setProperty);
        }
    }
}
