package com.phodal.migrator.core;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 步骤执行结果
 */
public class StepResult {
    private final boolean success;
    private final String message;
    private final Throwable error;
    private final Map<String, Object> details;
    private final Instant startTime;
    private final Instant endTime;
    private final ResultType type;
    
    private StepResult(boolean success, String message, Throwable error, 
                      Map<String, Object> details, Instant startTime, Instant endTime, ResultType type) {
        this.success = success;
        this.message = message;
        this.error = error;
        this.details = details != null ? new HashMap<>(details) : new HashMap<>();
        this.startTime = startTime;
        this.endTime = endTime;
        this.type = type;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public Throwable getError() {
        return error;
    }
    
    public Map<String, Object> getDetails() {
        return details;
    }
    
    public Instant getStartTime() {
        return startTime;
    }
    
    public Instant getEndTime() {
        return endTime;
    }
    
    public Duration getDuration() {
        return Duration.between(startTime, endTime);
    }
    
    public ResultType getType() {
        return type;
    }
    
    public boolean isWarning() {
        return type == ResultType.WARNING;
    }
    
    // 静态工厂方法
    public static StepResult success() {
        return success("操作成功完成");
    }
    
    public static StepResult success(String message) {
        Instant now = Instant.now();
        return new StepResult(true, message, null, null, now, now, ResultType.SUCCESS);
    }
    
    public static StepResult failure(String message) {
        Instant now = Instant.now();
        return new StepResult(false, message, null, null, now, now, ResultType.FAILURE);
    }
    
    public static StepResult failure(String message, Throwable error) {
        Instant now = Instant.now();
        return new StepResult(false, message, error, null, now, now, ResultType.FAILURE);
    }
    
    public static StepResult warning(String message) {
        Instant now = Instant.now();
        return new StepResult(true, message, null, null, now, now, ResultType.WARNING);
    }
    
    // Builder 模式
    public static Builder builder() {
        return new Builder();
    }
    
    public StepResult addDetail(String key, Object value) {
        this.details.put(key, value);
        return this;
    }
    
    public static class Builder {
        private boolean success = true;
        private String message = "";
        private Throwable error = null;
        private Map<String, Object> details = new HashMap<>();
        private Instant startTime = Instant.now();
        private Instant endTime = Instant.now();
        private ResultType type = ResultType.SUCCESS;
        
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder message(String message) {
            this.message = message;
            return this;
        }
        
        public Builder error(Throwable error) {
            this.error = error;
            this.success = false;
            this.type = ResultType.FAILURE;
            return this;
        }
        
        public Builder detail(String key, Object value) {
            this.details.put(key, value);
            return this;
        }
        
        public Builder details(Map<String, Object> details) {
            this.details.putAll(details);
            return this;
        }
        
        public Builder startTime(Instant startTime) {
            this.startTime = startTime;
            return this;
        }
        
        public Builder endTime(Instant endTime) {
            this.endTime = endTime;
            return this;
        }
        
        public Builder type(ResultType type) {
            this.type = type;
            return this;
        }
        
        public StepResult build() {
            return new StepResult(success, message, error, details, startTime, endTime, type);
        }
    }
    
    public enum ResultType {
        SUCCESS,
        WARNING,
        FAILURE
    }
}
