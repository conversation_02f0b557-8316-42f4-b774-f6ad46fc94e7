package com.phodal.migrator.core;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

/**
 * 迁移结果
 */
public class MigrationResult {
    private final boolean success;
    private final String message;
    private final MigrationStats stats;
    private final List<StepResult> stepResults;
    private final Instant startTime;
    private final Instant endTime;
    
    private MigrationResult(boolean success, String message, MigrationStats stats, 
                           List<StepResult> stepResults, Instant startTime, Instant endTime) {
        this.success = success;
        this.message = message;
        this.stats = stats;
        this.stepResults = stepResults;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public MigrationStats getStats() {
        return stats;
    }
    
    public List<StepResult> getStepResults() {
        return stepResults;
    }
    
    public Instant getStartTime() {
        return startTime;
    }
    
    public Instant getEndTime() {
        return endTime;
    }
    
    public Duration getTotalDuration() {
        return Duration.between(startTime, endTime);
    }
    
    // 静态工厂方法
    public static MigrationResult success(MigrationStats stats) {
        return success("迁移成功完成", stats, List.of());
    }
    
    public static MigrationResult success(String message, MigrationStats stats, List<StepResult> stepResults) {
        Instant now = Instant.now();
        return new MigrationResult(true, message, stats, stepResults, now, now);
    }
    
    public static MigrationResult failure(String message) {
        return failure(message, new MigrationStats(), List.of());
    }
    
    public static MigrationResult failure(String message, MigrationStats stats) {
        return failure(message, stats, List.of());
    }
    
    public static MigrationResult failure(String message, MigrationStats stats, List<StepResult> stepResults) {
        Instant now = Instant.now();
        return new MigrationResult(false, message, stats, stepResults, now, now);
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    public static class Builder {
        private boolean success = true;
        private String message = "";
        private MigrationStats stats = new MigrationStats();
        private List<StepResult> stepResults = List.of();
        private Instant startTime = Instant.now();
        private Instant endTime = Instant.now();
        
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder message(String message) {
            this.message = message;
            return this;
        }
        
        public Builder stats(MigrationStats stats) {
            this.stats = stats;
            return this;
        }
        
        public Builder stepResults(List<StepResult> stepResults) {
            this.stepResults = stepResults;
            return this;
        }
        
        public Builder startTime(Instant startTime) {
            this.startTime = startTime;
            return this;
        }
        
        public Builder endTime(Instant endTime) {
            this.endTime = endTime;
            return this;
        }
        
        public MigrationResult build() {
            return new MigrationResult(success, message, stats, stepResults, startTime, endTime);
        }
    }
}
