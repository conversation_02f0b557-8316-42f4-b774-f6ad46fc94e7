package com.phodal.migrator.core;

/**
 * 项目类型枚举
 */
public enum ProjectType {
    MAVEN("Maven", "pom.xml"),
    GRADLE("Gradle", "build.gradle"),
    GRADLE_KTS("Gradle Kotlin DSL", "build.gradle.kts"),
    UNKNOWN("Unknown", "");
    
    private final String displayName;
    private final String buildFile;
    
    ProjectType(String displayName, String buildFile) {
        this.displayName = displayName;
        this.buildFile = buildFile;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getBuildFile() {
        return buildFile;
    }
    
    /**
     * 根据构建文件名检测项目类型
     */
    public static ProjectType detectFromBuildFile(String fileName) {
        return switch (fileName) {
            case "pom.xml" -> MAVEN;
            case "build.gradle" -> GRADLE;
            case "build.gradle.kts" -> GRADLE_KTS;
            default -> UNKNOWN;
        };
    }
}
