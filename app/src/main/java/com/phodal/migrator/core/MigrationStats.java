package com.phodal.migrator.core;

import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 迁移统计信息
 */
public class MigrationStats {
    private Instant startTime;
    private Instant endTime;
    private boolean success;
    private String error;
    
    // 文件统计
    private int filesAnalyzed;
    private int filesModified;
    private int filesCreated;
    private int filesDeleted;
    
    // 依赖统计
    private int dependenciesUpgraded;
    private int dependenciesAdded;
    private int dependenciesRemoved;
    
    // 构建统计
    private int errorsFixed;
    private int buildAttempts;
    
    // 测试统计
    private int testsRun;
    private int testsPassed;
    private int testsFailed;
    
    // 步骤统计
    private final Map<String, StepResult> stepResults = new HashMap<>();
    
    public MigrationStats() {
        this.startTime = Instant.now();
        this.success = false;
    }
    
    // Getters and Setters
    public Instant getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }
    
    public Instant getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }
    
    public Duration getTotalDuration() {
        if (startTime != null && endTime != null) {
            return Duration.between(startTime, endTime);
        }
        return Duration.ZERO;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getError() {
        return error;
    }
    
    public void setError(String error) {
        this.error = error;
    }
    
    // 文件统计
    public int getFilesAnalyzed() {
        return filesAnalyzed;
    }
    
    public void setFilesAnalyzed(int filesAnalyzed) {
        this.filesAnalyzed = filesAnalyzed;
    }
    
    public int getFilesModified() {
        return filesModified;
    }
    
    public void setFilesModified(int filesModified) {
        this.filesModified = filesModified;
    }
    
    public int getFilesCreated() {
        return filesCreated;
    }
    
    public void setFilesCreated(int filesCreated) {
        this.filesCreated = filesCreated;
    }
    
    public int getFilesDeleted() {
        return filesDeleted;
    }
    
    public void setFilesDeleted(int filesDeleted) {
        this.filesDeleted = filesDeleted;
    }
    
    // 依赖统计
    public int getDependenciesUpgraded() {
        return dependenciesUpgraded;
    }
    
    public void setDependenciesUpgraded(int dependenciesUpgraded) {
        this.dependenciesUpgraded = dependenciesUpgraded;
    }
    
    public int getDependenciesAdded() {
        return dependenciesAdded;
    }
    
    public void setDependenciesAdded(int dependenciesAdded) {
        this.dependenciesAdded = dependenciesAdded;
    }
    
    public int getDependenciesRemoved() {
        return dependenciesRemoved;
    }
    
    public void setDependenciesRemoved(int dependenciesRemoved) {
        this.dependenciesRemoved = dependenciesRemoved;
    }
    
    // 构建统计
    public int getErrorsFixed() {
        return errorsFixed;
    }
    
    public void setErrorsFixed(int errorsFixed) {
        this.errorsFixed = errorsFixed;
    }
    
    public int getBuildAttempts() {
        return buildAttempts;
    }
    
    public void setBuildAttempts(int buildAttempts) {
        this.buildAttempts = buildAttempts;
    }
    
    // 测试统计
    public int getTestsRun() {
        return testsRun;
    }
    
    public void setTestsRun(int testsRun) {
        this.testsRun = testsRun;
    }
    
    public int getTestsPassed() {
        return testsPassed;
    }
    
    public void setTestsPassed(int testsPassed) {
        this.testsPassed = testsPassed;
    }
    
    public int getTestsFailed() {
        return testsFailed;
    }
    
    public void setTestsFailed(int testsFailed) {
        this.testsFailed = testsFailed;
    }
    
    // 步骤统计
    public void addStepResult(String stepName, StepResult result) {
        stepResults.put(stepName, result);
    }
    
    public Map<String, StepResult> getStepResults() {
        return stepResults;
    }
    
    // 便捷方法
    public void incrementFilesAnalyzed() {
        this.filesAnalyzed++;
    }
    
    public void incrementFilesModified() {
        this.filesModified++;
    }
    
    public void incrementDependenciesUpgraded() {
        this.dependenciesUpgraded++;
    }
    
    public void incrementErrorsFixed() {
        this.errorsFixed++;
    }
    
    public void incrementBuildAttempts() {
        this.buildAttempts++;
    }
}
