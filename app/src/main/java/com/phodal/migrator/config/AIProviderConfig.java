package com.phodal.migrator.config;

import io.github.cdimascio.dotenv.Dotenv;

/**
 * AI 提供商配置类
 * 管理不同 AI 提供商的配置信息，包括 API 密钥、基础 URL 等
 */
public class AIProviderConfig {
    
    private static final Dotenv dotenv = Dotenv.configure()
            .ignoreIfMalformed()
            .ignoreIfMissing()
            .load();
    
    /**
     * AI 提供商类型
     */
    public enum ProviderType {
        DEEPSEEK("DeepSeek", "https://api.deepseek.com/v1", "deepseek-chat"),
        CHATGLM("ChatGLM", "https://open.bigmodel.cn/api/paas/v4", "glm-4-air"),
        OPENAI("OpenAI", "https://api.openai.com/v1", "gpt-3.5-turbo");
        
        private final String displayName;
        private final String baseUrl;
        private final String defaultModel;
        
        ProviderType(String displayName, String baseUrl, String defaultModel) {
            this.displayName = displayName;
            this.baseUrl = baseUrl;
            this.defaultModel = defaultModel;
        }
        
        public String getDisplayName() { return displayName; }
        public String getBaseUrl() { return baseUrl; }
        public String getDefaultModel() { return defaultModel; }
    }
    
    /**
     * AI 提供商配置
     */
    public static class ProviderConfig {
        private final ProviderType type;
        private final String apiKey;
        private final String baseUrl;
        private final String model;
        
        public ProviderConfig(ProviderType type, String apiKey, String baseUrl, String model) {
            this.type = type;
            this.apiKey = apiKey;
            this.baseUrl = baseUrl;
            this.model = model;
        }
        
        public ProviderType getType() { return type; }
        public String getApiKey() { return apiKey; }
        public String getBaseUrl() { return baseUrl; }
        public String getModel() { return model; }
        public String getDisplayName() { return type.getDisplayName(); }
    }
    
    /**
     * 获取可用的 AI 提供商配置
     * 按优先级返回第一个可用的配置
     */
    public static ProviderConfig getAvailableProvider() {
        // 检查 DeepSeek
        String deepseekToken = dotenv.get("DEEPSEEK_TOKEN");
        if (deepseekToken != null && !deepseekToken.trim().isEmpty()) {
            return new ProviderConfig(
                ProviderType.DEEPSEEK,
                deepseekToken,
                ProviderType.DEEPSEEK.getBaseUrl(),
                ProviderType.DEEPSEEK.getDefaultModel()
            );
        }
        
        // 检查 ChatGLM
        String glmApiKey = dotenv.get("GLM_API_KEY");
        if (glmApiKey == null || glmApiKey.trim().isEmpty()) {
            glmApiKey = dotenv.get("GLM_TOKEN");
        }
        if (glmApiKey != null && !glmApiKey.trim().isEmpty()) {
            return new ProviderConfig(
                ProviderType.CHATGLM,
                glmApiKey,
                ProviderType.CHATGLM.getBaseUrl(),
                ProviderType.CHATGLM.getDefaultModel()
            );
        }
        
        // 检查 OpenAI
        String openaiKey = dotenv.get("OPENAI_API_KEY");
        if (openaiKey != null && !openaiKey.trim().isEmpty()) {
            return new ProviderConfig(
                ProviderType.OPENAI,
                openaiKey,
                ProviderType.OPENAI.getBaseUrl(),
                ProviderType.OPENAI.getDefaultModel()
            );
        }
        
        throw new IllegalStateException("没有找到可用的 AI 提供商配置。请在 .env 文件中设置 DEEPSEEK_TOKEN、GLM_API_KEY 或 OPENAI_API_KEY");
    }
    
    /**
     * 获取指定类型的提供商配置
     */
    public static ProviderConfig getProviderConfig(ProviderType type) {
        switch (type) {
            case DEEPSEEK:
                String deepseekToken = dotenv.get("DEEPSEEK_TOKEN");
                if (deepseekToken == null || deepseekToken.trim().isEmpty()) {
                    throw new IllegalStateException("DEEPSEEK_TOKEN 未设置");
                }
                return new ProviderConfig(type, deepseekToken, type.getBaseUrl(), type.getDefaultModel());
                
            case CHATGLM:
                String glmApiKey = dotenv.get("GLM_API_KEY");
                if (glmApiKey == null || glmApiKey.trim().isEmpty()) {
                    glmApiKey = dotenv.get("GLM_TOKEN");
                }
                if (glmApiKey == null || glmApiKey.trim().isEmpty()) {
                    throw new IllegalStateException("GLM_API_KEY 或 GLM_TOKEN 未设置");
                }
                return new ProviderConfig(type, glmApiKey, type.getBaseUrl(), type.getDefaultModel());
                
            case OPENAI:
                String openaiKey = dotenv.get("OPENAI_API_KEY");
                if (openaiKey == null || openaiKey.trim().isEmpty()) {
                    throw new IllegalStateException("OPENAI_API_KEY 未设置");
                }
                return new ProviderConfig(type, openaiKey, type.getBaseUrl(), type.getDefaultModel());
                
            default:
                throw new IllegalArgumentException("不支持的 AI 提供商类型: " + type);
        }
    }
    
    /**
     * 检查指定提供商是否可用
     */
    public static boolean isProviderAvailable(ProviderType type) {
        try {
            getProviderConfig(type);
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }
}
