package com.phodal.migrator.config;

import java.util.List;
import java.util.Map;

/**
 * 迁移配置类
 * 支持从外部配置文件加载迁移参数
 */
public class MigrationConfig {
    
    private TargetVersions targetVersions;
    private List<RuleConfig> rules;
    private AIConfig ai;
    private BuildConfig build;
    
    public TargetVersions getTargetVersions() { return targetVersions; }
    public void setTargetVersions(TargetVersions targetVersions) { this.targetVersions = targetVersions; }
    
    public List<RuleConfig> getRules() { return rules; }
    public void setRules(List<RuleConfig> rules) { this.rules = rules; }
    
    public AIConfig getAi() { return ai; }
    public void setAi(AIConfig ai) { this.ai = ai; }
    
    public BuildConfig getBuild() { return build; }
    public void setBuild(BuildConfig build) { this.build = build; }
    
    /**
     * 目标版本配置
     */
    public static class TargetVersions {
        private String springBoot;
        private String java;
        
        public String getSpringBoot() { return springBoot; }
        public void setSpringBoot(String springBoot) { this.springBoot = springBoot; }
        
        public String getJava() { return java; }
        public void setJava(String java) { this.java = java; }
    }
    
    /**
     * 规则配置
     */
    public static class RuleConfig {
        private String name;
        private boolean enabled;
        private int priority;
        private Map<String, Object> parameters;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public int getPriority() { return priority; }
        public void setPriority(int priority) { this.priority = priority; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }
    
    /**
     * AI配置
     */
    public static class AIConfig {
        private String provider;
        private int maxRetries;
        private int timeoutSeconds;
        private String apiKey;
        
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        
        public int getMaxRetries() { return maxRetries; }
        public void setMaxRetries(int maxRetries) { this.maxRetries = maxRetries; }
        
        public int getTimeoutSeconds() { return timeoutSeconds; }
        public void setTimeoutSeconds(int timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }
        
        public String getApiKey() { return apiKey; }
        public void setApiKey(String apiKey) { this.apiKey = apiKey; }
    }
    
    /**
     * 构建配置
     */
    public static class BuildConfig {
        private String command;
        private int maxAttempts;
        private List<String> skipTests;
        
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
        
        public int getMaxAttempts() { return maxAttempts; }
        public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
        
        public List<String> getSkipTests() { return skipTests; }
        public void setSkipTests(List<String> skipTests) { this.skipTests = skipTests; }
    }
}
