package com.phodal.migrator.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证器
 * 验证迁移配置的有效性
 */
public class ConfigValidator {
    private static final Logger logger = LoggerFactory.getLogger(ConfigValidator.class);
    
    /**
     * 验证迁移配置
     * @param config 迁移配置
     * @return 验证结果
     */
    public static ValidationResult validate(MigrationConfig config) {
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        if (config == null) {
            errors.add("配置不能为空");
            return new ValidationResult(false, errors, warnings);
        }
        
        // 验证目标版本
        validateTargetVersions(config.getTargetVersions(), errors, warnings);
        
        // 验证AI配置
        validateAIConfig(config.getAi(), errors, warnings);
        
        // 验证构建配置
        validateBuildConfig(config.getBuild(), errors, warnings);
        
        // 验证规则配置
        validateRules(config.getRules(), errors, warnings);
        
        boolean isValid = errors.isEmpty();
        
        if (!isValid) {
            logger.error("配置验证失败: {}", String.join(", ", errors));
        }
        
        if (!warnings.isEmpty()) {
            logger.warn("配置警告: {}", String.join(", ", warnings));
        }
        
        return new ValidationResult(isValid, errors, warnings);
    }
    
    private static void validateTargetVersions(MigrationConfig.TargetVersions targetVersions, 
                                             List<String> errors, List<String> warnings) {
        if (targetVersions == null) {
            warnings.add("未配置目标版本，将使用默认值");
            return;
        }
        
        // 验证Spring Boot版本
        String springBootVersion = targetVersions.getSpringBoot();
        if (springBootVersion == null || springBootVersion.trim().isEmpty()) {
            warnings.add("未配置Spring Boot目标版本，将使用默认值");
        } else if (!isValidSpringBootVersion(springBootVersion)) {
            errors.add("无效的Spring Boot版本: " + springBootVersion);
        }
        
        // 验证Java版本
        String javaVersion = targetVersions.getJava();
        if (javaVersion == null || javaVersion.trim().isEmpty()) {
            warnings.add("未配置Java目标版本，将使用默认值");
        } else if (!isValidJavaVersion(javaVersion)) {
            errors.add("无效的Java版本: " + javaVersion);
        }
    }
    
    private static void validateAIConfig(MigrationConfig.AIConfig aiConfig, 
                                       List<String> errors, List<String> warnings) {
        if (aiConfig == null) {
            warnings.add("未配置AI服务，将使用默认配置");
            return;
        }
        
        // 验证AI提供商
        String provider = aiConfig.getProvider();
        if (provider != null && !isValidAIProvider(provider)) {
            warnings.add("未知的AI提供商: " + provider);
        }
        
        // 验证重试次数
        int maxRetries = aiConfig.getMaxRetries();
        if (maxRetries < 0 || maxRetries > 10) {
            warnings.add("AI最大重试次数建议在0-10之间，当前值: " + maxRetries);
        }
        
        // 验证超时时间
        int timeoutSeconds = aiConfig.getTimeoutSeconds();
        if (timeoutSeconds < 5 || timeoutSeconds > 300) {
            warnings.add("AI超时时间建议在5-300秒之间，当前值: " + timeoutSeconds);
        }
    }
    
    private static void validateBuildConfig(MigrationConfig.BuildConfig buildConfig, 
                                          List<String> errors, List<String> warnings) {
        if (buildConfig == null) {
            warnings.add("未配置构建参数，将使用默认配置");
            return;
        }
        
        // 验证最大尝试次数
        int maxAttempts = buildConfig.getMaxAttempts();
        if (maxAttempts < 1 || maxAttempts > 20) {
            warnings.add("构建最大尝试次数建议在1-20之间，当前值: " + maxAttempts);
        }
    }
    
    private static void validateRules(List<MigrationConfig.RuleConfig> rules, 
                                    List<String> errors, List<String> warnings) {
        if (rules == null || rules.isEmpty()) {
            warnings.add("未配置迁移规则，将使用默认规则");
            return;
        }
        
        for (MigrationConfig.RuleConfig rule : rules) {
            if (rule.getName() == null || rule.getName().trim().isEmpty()) {
                errors.add("规则名称不能为空");
                continue;
            }
            
            if (rule.getPriority() < 0 || rule.getPriority() > 100) {
                warnings.add("规则优先级建议在0-100之间: " + rule.getName());
            }
        }
    }
    
    private static boolean isValidSpringBootVersion(String version) {
        // 简单的版本格式验证
        return version.matches("\\d+\\.\\d+\\.\\d+(-.*)?");
    }
    
    private static boolean isValidJavaVersion(String version) {
        // 支持的Java版本: 8, 11, 17, 21
        return version.matches("(8|11|17|21)");
    }
    
    private static boolean isValidAIProvider(String provider) {
        return List.of("phodal", "openai", "claude", "local").contains(provider.toLowerCase());
    }
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        private final List<String> warnings;
        
        public ValidationResult(boolean valid, List<String> errors, List<String> warnings) {
            this.valid = valid;
            this.errors = errors;
            this.warnings = warnings;
        }
        
        public boolean isValid() { return valid; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        
        public boolean hasWarnings() { return !warnings.isEmpty(); }
        public boolean hasErrors() { return !errors.isEmpty(); }
    }
}
