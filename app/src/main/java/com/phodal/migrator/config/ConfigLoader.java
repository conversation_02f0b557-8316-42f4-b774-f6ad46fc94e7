package com.phodal.migrator.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

/**
 * 配置加载器
 * 支持从YAML和Properties文件加载配置
 */
public class ConfigLoader {
    private static final Logger logger = LoggerFactory.getLogger(ConfigLoader.class);
    
    private static final String DEFAULT_CONFIG_FILE = "migration-config.yml";
    private static final ObjectMapper yamlMapper = new ObjectMapper(new YAMLFactory());
    
    /**
     * 加载迁移配置
     * @param configPath 配置文件路径，如果为null则使用默认配置
     * @return 迁移配置
     */
    public static MigrationConfig loadConfig(Path configPath) {
        MigrationConfig config;

        try {
            if (configPath != null && Files.exists(configPath)) {
                logger.info("加载配置文件: {}", configPath);
                config = loadFromFile(configPath);
            } else {
                logger.info("使用默认配置");
                config = loadDefaultConfig();
            }
        } catch (Exception e) {
            logger.warn("配置加载失败，使用默认配置: {}", e.getMessage());
            config = createDefaultConfig();
        }

        // 验证配置
        ConfigValidator.ValidationResult validation = ConfigValidator.validate(config);
        if (!validation.isValid()) {
            logger.error("配置验证失败: {}", String.join(", ", validation.getErrors()));
            throw new IllegalArgumentException("配置验证失败: " + String.join(", ", validation.getErrors()));
        }

        if (validation.hasWarnings()) {
            logger.warn("配置警告: {}", String.join(", ", validation.getWarnings()));
        }

        return config;
    }
    
    /**
     * 从文件加载配置
     */
    private static MigrationConfig loadFromFile(Path configPath) throws IOException {
        String fileName = configPath.getFileName().toString().toLowerCase();
        
        if (fileName.endsWith(".yml") || fileName.endsWith(".yaml")) {
            return yamlMapper.readValue(Files.newInputStream(configPath), MigrationConfig.class);
        } else if (fileName.endsWith(".properties")) {
            return loadFromProperties(configPath);
        } else {
            throw new IllegalArgumentException("不支持的配置文件格式: " + fileName);
        }
    }
    
    /**
     * 从Properties文件加载配置
     */
    private static MigrationConfig loadFromProperties(Path configPath) throws IOException {
        Properties props = new Properties();
        props.load(Files.newInputStream(configPath));
        
        MigrationConfig config = new MigrationConfig();
        
        // 设置目标版本
        MigrationConfig.TargetVersions targetVersions = new MigrationConfig.TargetVersions();
        targetVersions.setSpringBoot(props.getProperty("migration.target-versions.spring-boot", "3.2.0"));
        targetVersions.setJava(props.getProperty("migration.target-versions.java", "21"));
        config.setTargetVersions(targetVersions);
        
        // 设置AI配置
        MigrationConfig.AIConfig aiConfig = new MigrationConfig.AIConfig();
        aiConfig.setProvider(props.getProperty("migration.ai.provider", "phodal"));
        aiConfig.setMaxRetries(Integer.parseInt(props.getProperty("migration.ai.max-retries", "3")));
        aiConfig.setTimeoutSeconds(Integer.parseInt(props.getProperty("migration.ai.timeout", "30")));
        config.setAi(aiConfig);
        
        return config;
    }
    
    /**
     * 加载默认配置（从classpath）
     */
    private static MigrationConfig loadDefaultConfig() throws IOException {
        try (InputStream is = ConfigLoader.class.getClassLoader().getResourceAsStream(DEFAULT_CONFIG_FILE)) {
            if (is != null) {
                return yamlMapper.readValue(is, MigrationConfig.class);
            } else {
                return createDefaultConfig();
            }
        }
    }
    
    /**
     * 创建默认配置
     */
    private static MigrationConfig createDefaultConfig() {
        MigrationConfig config = new MigrationConfig();
        
        // 目标版本
        MigrationConfig.TargetVersions targetVersions = new MigrationConfig.TargetVersions();
        targetVersions.setSpringBoot("3.2.0");
        targetVersions.setJava("21");
        config.setTargetVersions(targetVersions);
        
        // AI配置
        MigrationConfig.AIConfig aiConfig = new MigrationConfig.AIConfig();
        aiConfig.setProvider("phodal");
        aiConfig.setMaxRetries(3);
        aiConfig.setTimeoutSeconds(30);
        config.setAi(aiConfig);
        
        // 构建配置
        MigrationConfig.BuildConfig buildConfig = new MigrationConfig.BuildConfig();
        buildConfig.setMaxAttempts(5);
        config.setBuild(buildConfig);
        
        return config;
    }
}
