package com.phodal.migrator.application;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.StepResult;
import com.phodal.migrator.service.dependency.DependencyMigrationService;
import com.phodal.migrator.service.dependency.DependencyMigrationServiceImpl;

/**
 * 步骤3: 依赖升级
 * 
 * 功能：
 * - 升级 Spring Boot 版本到 3.x
 * - 升级相关依赖库
 * - 添加 Jakarta EE 依赖
 * - 移除不兼容的依赖
 */
public class DependencyMigrationStep extends AbstractMigrationStep {

    private final DependencyMigrationService dependencyService;

    public DependencyMigrationStep() {
        this.dependencyService = new DependencyMigrationServiceImpl();
    }
    
    @Override
    public String getName() {
        return "dependency_migration";
    }
    
    @Override
    public String getDescription() {
        return "升级Maven/Gradle依赖，Spring Boot 2.x → 3.x";
    }
    
    @Override
    public int getEstimatedDurationSeconds() {
        return 90;
    }
    
    @Override
    protected StepResult doExecute(MigrationContext context) {
        try {
            logger.info("开始依赖升级...");

            // 使用服务层进行依赖迁移
            DependencyMigrationService.DependencyMigrationResult result = dependencyService.migrateDependencies(context);

            if (result.isSuccess()) {
                logger.info("依赖升级完成");
                return StepResult.success(result.getMessage())
                    .addDetail("upgraded_dependencies", result.getUpgradedDependencies())
                    .addDetail("added_dependencies", result.getAddedDependencies())
                    .addDetail("removed_dependencies", result.getRemovedDependencies());
            } else {
                return StepResult.failure(result.getMessage());
            }

        } catch (Exception e) {
            return StepResult.failure("依赖升级失败: " + e.getMessage(), e);
        }
    }
}
