package com.phodal.migrator.application;

import com.phodal.migrator.core.MigrationContext;
import com.phodal.migrator.core.StepResult;
import com.phodal.migrator.service.code.CodeMigrationService;
import com.phodal.migrator.service.code.CodeMigrationServiceImpl;

/**
 * 步骤4: 代码转换
 * 
 * 功能：
 * - javax.* → jakarta.* 包名转换
 * - 过时API替换
 * - Spring Boot 3.x API适配
 * - JDK 21 新特性应用
 */
public class CodeMigrationStep extends AbstractMigrationStep {

    private final CodeMigrationService codeService;

    public CodeMigrationStep() {
        this.codeService = new CodeMigrationServiceImpl();
    }
    
    @Override
    public String getName() {
        return "code_migration";
    }
    
    @Override
    public String getDescription() {
        return "代码转换，javax → jakarta，API升级";
    }
    
    @Override
    public int getEstimatedDurationSeconds() {
        return 120;
    }
    
    @Override
    protected StepResult doExecute(MigrationContext context) {
        try {
            logger.info("开始代码转换...");

            // 使用服务层进行代码迁移
            CodeMigrationService.CodeMigrationResult result = codeService.migrateCode(context);

            if (result.isSuccess()) {
                logger.info("代码转换完成，修改了 {} 个文件", result.getModifiedFiles());
                return StepResult.success(result.getMessage())
                    .addDetail("modified_files", result.getModifiedFiles())
                    .addDetail("transformation_counts", result.getTransformationCounts())
                    .addDetail("errors", result.getErrors());
            } else {
                return StepResult.failure(result.getMessage())
                    .addDetail("errors", result.getErrors());
            }

        } catch (Exception e) {
            return StepResult.failure("代码转换失败: " + e.getMessage(), e);
        }
    }
}
