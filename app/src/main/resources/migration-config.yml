# Spring Boot 迁移工具配置文件

# 目标版本配置
targetVersions:
  springBoot: "3.2.0"
  java: "21"
# 迁移规则配置
rules:
  - name: "javax-to-jakarta"
    enabled: true
    priority: 1
    parameters:
      excludePackages: []

  - name: "spring-security-config"
    enabled: true
    priority: 2
    parameters:
      updateMethodSecurity: true

  - name: "spring-boot-properties"
    enabled: true
    priority: 3
    parameters:
      removeDeprecated: true

  - name: "jdk21-features"
    enabled: false
    priority: 10
    parameters:
      applyPatternMatching: true
      applyTextBlocks: true
# AI服务配置
ai:
  provider: "phodal"
  maxRetries: 3
  timeoutSeconds: 30
  # apiKey: "your-api-key-here"

# 构建配置
build:
  command: "mvn clean compile"
  maxAttempts: 5
  skipTests:
    - "integration-tests"
