/*
 * This file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java application project to get you started.
 * For more details on building Java & JVM projects, please refer to https://docs.gradle.org/8.14.1/userguide/building_java_projects.html in the Gradle documentation.
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    // Apply the application plugin to add support for building a CLI application in Java.
    application
}

repositories {
    // Use Maven Central for resolving dependencies.
    mavenCentral()
}

dependencies {
    // Core utilities
    implementation(libs.guava)

    // CLI Framework
    implementation(libs.picocli)

    // Code Analysis and Transformation
    implementation(libs.javaparser.core)
    implementation(platform(libs.openrewrite.bom))
    implementation(libs.openrewrite.java)
    implementation(libs.openrewrite.maven)
    implementation(libs.openrewrite.xml)
    implementation(libs.openrewrite.java21)

    // Logging
    implementation(libs.slf4j.api)
    implementation(libs.logback.classic)

    // JSON/YAML Processing
    implementation(libs.jackson.core)
    implementation(libs.jackson.databind)
    implementation(libs.jackson.yaml)

    // Template Engine
    implementation(libs.freemarker)

    // HTML/XML Processing
    implementation(libs.jsoup)

    // Bytecode Analysis
    implementation(libs.asm)

    // Testing
    testImplementation(libs.junit.jupiter)
    testImplementation(libs.mockito.core)
    testImplementation(libs.mockito.junit.jupiter)
    testImplementation(libs.assertj.core)
}

testing {
    suites {
        // Configure the built-in test suite
        val test by getting(JvmTestSuite::class) {
            // Use JUnit Jupiter test framework
            useJUnitJupiter("5.12.1")
        }
    }
}

// Apply a specific Java toolchain to ease working on different environments.
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

application {
    // Define the main class for the application.
    mainClass = "com.phodal.migrator.cli.JavaMigratorCLI"
}
