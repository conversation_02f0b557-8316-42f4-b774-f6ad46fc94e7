# Spring Boot 迁移工具使用指南

## 🚀 快速开始

### 构建项目

```bash
./gradlew build
```

### 运行迁移工具

```bash
# 完整迁移
./gradlew run --args="migrate /path/to/your/spring-boot-project"

# 预览模式（推荐先运行）
./gradlew run --args="migrate /path/to/your/spring-boot-project --dry-run"

# 详细输出
./gradlew run --args="migrate /path/to/your/spring-boot-project --verbose"
```

## 📋 命令行选项

### 基本命令

```bash
# 显示帮助
./gradlew run --args="--help"

# 显示版本
./gradlew run --args="--version"

# 仅分析项目
./gradlew run --args="analyze /path/to/project"
```

### 迁移选项

```bash
# 预览模式 - 不实际修改文件
--dry-run

# 详细输出
--verbose

# 指定目标路径
--target-path /path/to/target

# 启用AI修复
--enable-ai-fix

# 设置最大构建尝试次数
--max-build-attempts 5

# 跳过特定步骤
--skip-steps config,test,runtime

# 自定义构建命令
--build-command "mvn clean compile -DskipTests"

# 指定报告格式
--report-format markdown,json,html

# 指定AI服务提供商
--ai-provider phodal
```

## 🔄 迁移流程

工具执行以下8个步骤：

1. **项目分析与备份** - 分析项目结构，创建备份
2. **配置文件迁移** - 更新 application.yml/properties
3. **依赖升级** - 升级 Maven/Gradle 依赖
4. **代码转换** - javax → jakarta，API升级
5. **构建修复** - 修复编译错误
6. **测试执行** - 运行测试验证
7. **运行时验证** - 启动应用验证
8. **报告生成** - 生成迁移报告

## 📊 使用示例

### 示例1: 基本迁移

```bash
./gradlew run --args="migrate ./example-project"
```

### 示例2: 预览模式

```bash
./gradlew run --args="migrate ./example-project --dry-run --verbose"
```

### 示例3: 跳过测试和运行时验证

```bash
./gradlew run --args="migrate ./example-project --skip-steps test_execution,runtime_validation"
```

### 示例4: 启用AI修复

```bash
./gradlew run --args="migrate ./example-project --enable-ai-fix --max-build-attempts 10"
```

## 🧪 测试示例项目

项目包含一个测试用的Spring Boot应用：

```bash
# 分析测试项目
./gradlew run --args="analyze _fixtures/test-springboot-app"

# 迁移测试项目（预览模式）
./gradlew run --args="migrate _fixtures/test-springboot-app --dry-run --verbose"
```

## 🔧 开发和测试

### 运行单元测试

```bash
./gradlew test
```

### 运行示例代码

```bash
./gradlew run --args="com.phodal.migrator.example.MigratorExample"
```

### 调试模式

```bash
./gradlew run --debug-jvm --args="migrate /path/to/project --verbose"
```

## 📝 输出和报告

### 控制台输出

工具会在控制台显示：
- 🚀 迁移开始信息
- 📋 每个步骤的进度
- ✅ 成功完成的步骤
- ⚠️ 警告信息
- ❌ 错误信息
- 📊 统计信息

### 生成的报告

默认生成 Markdown 格式报告，包含：
- 迁移统计信息
- 成功完成的步骤
- 需要手动处理的项目
- 主要变更列表
- 风险评估和建议

## ⚠️ 注意事项

1. **备份重要**: 虽然工具会自动创建备份，但建议手动备份重要项目
2. **预览优先**: 首次使用建议先运行 `--dry-run` 模式
3. **逐步验证**: 迁移后逐步验证应用功能
4. **版本兼容**: 确保目标环境支持 JDK 21 和 Spring Boot 3.x
5. **依赖检查**: 检查第三方依赖的兼容性

## 🐛 故障排除

### 常见问题

1. **项目路径不存在**
   ```
   确保项目路径正确且包含 pom.xml 或 build.gradle 文件
   ```

2. **权限不足**
   ```
   确保对项目目录有读写权限
   ```

3. **构建失败**
   ```
   使用 --max-build-attempts 增加重试次数
   启用 --enable-ai-fix 进行智能修复
   ```

4. **内存不足**
   ```
   export JAVA_OPTS="-Xmx2g"
   ./gradlew run --args="..."
   ```

### 获取帮助

- 使用 `--verbose` 获取详细日志
- 查看生成的迁移报告
- 检查项目的 GitHub Issues

## 🔗 相关链接

- [Spring Boot 3.x 迁移指南](https://spring.io/blog/2022/05/24/preparing-for-spring-boot-3-0)
- [Jakarta EE 迁移指南](https://jakarta.ee/resources/migration-guide/)
- [JDK 21 新特性](https://openjdk.org/projects/jdk/21/)
